# AI_Visual_Recognition

- 主要功能：基于大模型技术，实现对视频流中泡沫的识别和分析。

## 安装

- 默认使用 `python3.10` 环境
- 安装依赖：

```bash
pip install -r requirements.txt
```

- 配置环境变量：

  1. 复制 `.env.example` 文件并重命名为 `.env`；复制 `env.yaml.example` 文件并重命名为 `env.yaml`
  2. 编辑 `.env` 文件，配置模型相关环境变量
  3. 编辑 `env.yaml` 文件，配置相应参数
  4. 将 `assets/filter_aeration_failure_images.png` 图像文件放置到实际帧图片保存路径下

## 启动

### 启动方式 1（推荐生产环境）：

```bash
pm2 start bin/pm2/app_run.json
pm2 start bin/pm2/text.json
pm2 start bin/pm2/vision.json
```

### 启动方式 2（开发/调试）：

- 启动主程序：

```bash
python app_run.py
```

- 启动模型推理服务：

```bash
# 文本模型
CUDA_VISIBLE_DEVICES=0 swift deploy --model_type qwen2_5-7b-instruct-awq --infer_backend vllm --max_model_len 4096 --model_id_or_path llms/models/Qwen/Qwen2.5-7B-Instruct-AWQ --log_interval 0 --port 8000 --host '*************'

# 视觉模型
CUDA_VISIBLE_DEVICES=1 swift deploy --model_type qwen2-vl-7b-instruct-awq --infer_backend vllm --model_id_or_path llms/models/Qwen/Qwen2-VL-7B-Instruct-AWQ --log_interval 0 --port 8001 --host '*************' --limit_mm_per_prompt '{"image": 2}'
```

- 端口说明：

  - 文本模型：`8000`
  - 视觉模型：`8001`
  - 数据库服务：`8101`

## 测试

如需测试耙斗卡住和耙斗倾斜的识别功能，可在不连接摄像头的情况下使用测试模型：

1. 在 `server/task/cam_task.py` 文件末尾，将 `monitoring_system.start_test_mode()` 设为开启
2. 在 `self.test_images = []` 中添加需要测试的图片路径

---

## 目录结构

```text
opshub-ai/
├── .gitignore
├── app_run.py                # 启动文件
├── assets/                   # 测试图像
│   └── frame.jpg
├── configs/                  # 配置文件夹
│   ├── .env
│   └── env.yaml
├── config_file.py            # 配置加载
├── datasets/                 # 摄像头数据帧存放
│   └── __init__.py
├── docker/                   # Docker 镜像设置
│   ├── api/
│   │   ├── docker-compose.yml
│   │   └── Dockerfile
│   ├── chat/
│   │   ├── docker-compose.yml
│   │   └── Dockerfile
│   └── vl/
│       ├── docker-compose.yml
│       └── Dockerfile
├── llms/                     # 大模型推理服务
│   ├── config.py
│   ├── llm_api_server.py
│   ├── models/
│   │   ├── textmodel/
│   │   │   ├── download.py
│   │   │   ├── requirements.txt
│   │   │   └── startservice.txt
│   │   └── visionmodel/
│   │       ├── download.py
│   │       ├── requirements.txt
│   │       └── startservice.txt
│   ├── vllm_api_server.py
│   ├── vllm_api_server_multiple.py
│   └── __init__.py
├── logs/
│   └── __init__.py
├── requirements.txt
├── server/
│   ├── cam/
│   │   ├── api.py
│   │   └── entity.py
│   ├── remote/
│   │   ├── device.py
│   │   └── point.py
│   ├── task/
│   │   ├── cam_task.py
│   │   └── service.py
│   └── utils/
│       ├── cleanup.py
│       ├── data_publisher.py
│       ├── env_manager.py
│       ├── logger.py
│       ├── mqtt_tool.py
│       ├── path_manager.py
│       ├── pg_tool.py
│       ├── sensor_manager.py
│       └── video_processor.py
├── sql/
│   └── public.sql
└── tests/
    ├── test_rtsp.py
    └── __init__.py
```

---

```
