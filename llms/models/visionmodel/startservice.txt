进入项目的主目录

使用download.py 文件进行模型下载

CUDA_VISIBLE_DEVICES=1 swift deploy --model_type qwen2-vl-7b-instruct-awq --infer_backend vllm  --model_id_or_path llms/models/visionmodel/data --log_interval 0 --port 8001 --host '*************' --limit_mm_per_prompt '{"image": 2}'

参数说明: 

CUDA_VISIBLE_DEVICES 后缀参数为使用第几个GPU.例如:CUDA_VISIBLE_DEVICES=0 表示放在GPU 1上
swift 使用ms-swift框架进行推理模式,里面对vllm框架进行了封装
model_type 模型类型
infer_backend 使用什么推理框架,这里vllm框架进行部署
max_model_len 最大长度根据GPU显存设置,这里设置为4096
model_id_or_path 模型的实际下载地址.
log_interval 对统计信息进行打印的间隔
port 提供服务的端口号
host 提供服务的ip
limit_mm_per_prompt 多图推理的的时候要设置一次推理几张图片帧