import random
from typing import Optional
from llms.utils.utils import assemble_ai_dispatch_employee_data, assemble_ai_dispatch_task_data, convert_ai_dispatch_task_field_dict_to_dict, filter_employee_by_weekday
from pydantic import BaseModel
import collections
from enum import Enum
import time
import traceback
from llms.config import SYSTEM
from openai import OpenAI
import os
from datetime import datetime, timedelta
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import JSO<PERSON><PERSON>ponse, StreamingResponse
import json
from config_file import config  # 导入全局配置实例
from sse_starlette.sse import EventSourceResponse
from llms.utils.history import get_conversation_history, get_conversation_session, insert_conversation_message
from llms.utils.llm_task_tool import *
from llms.utils.opshub_api import get_alarm_real_time_data

MAX_RETRIES = 3

app = FastAPI()


class Intent(Enum):
    CHAT = "闲聊"
    TASK_ASSIGNMENT = "任务指派"
    WORK_SUGGESTION = "派工建议"

    @property
    def run(self) -> str:
        func_dict = {
            "闲聊": llm_process,
            "任务指派": llm_process,
            "派工建议": llm_process
        }
        return func_dict[self.value]


class BurstTasksRequest(BaseModel):
    user_input: str
    employee_list: list
    task_info: dict


class RoutineTasksRequest(BaseModel):
    task_record_list: list
    employee_list: list
    rule_list: list
    history: Optional[list] = None


class OptimizeRoutineTasksRequest(BaseModel):
    task_record_list: list
    employee_list: list
    select_option: list[str]


class AIDispatchTaskRequest(BaseModel):
    user_input: str
    employee_list: list
    task_info: dict
    history: Optional[list] = None


class GetStandardHourRequest(BaseModel):
    user_input: str


class GetAbnormalAlarmAnalysisRequest(BaseModel):
    """
    {
        "alarm_id": "241d6308-bba0-4e01-88a9-6f4481b050c1",
        "alarm_time": "2025-03-27 16:45:14",
        "stop_time": null,
        "alarm_detail": "进口电磁流量计 触发 设备离线报警 : 设备离线",
        "alarm_reason": null,
        "tackle_method": null,
        "device_id": "4064",
        "device_name": "进口电磁流量计",
        "point_id": null,
        "longitude": null,
        "latitude": null,
        "alarm_name": "设备离线报警",
        "point_name": null,
        "alarm_type_name": "设备报警",
        "alarm_mode": 0,
        "duration_time": 15518.56,
        "handle_flag": 0,
        "device_type": "生产设备"
    },
    """
    user_input: str
    alarm_id: str
    alarm_detail: str
    alarm_time: str
    device_id: str
    session_id: str


class GetHistoryRequest(BaseModel):
    session_id: str
    template_type: str


class GetSessionRequest(BaseModel):
    template_type: str


class GetAiSchedulingTaskWorkCycleRequest(BaseModel):
    beginTime: Optional[str] = None #开始时间
    endTime: Optional[str] = None #结束时间
    taskTypeId: Optional[int] = None #任务类型ID
    schedulingPlanTaskId: Optional[int] = None #排班任务ID

class GetAiSchedulingTaskRequest(BaseModel):
    schedulingPlanTaskId: Optional[int] = None #排班任务ID
    taskName: Optional[str] = None #任务名称
    skillIds: Optional[str] = None #技能ID
    skillNames: Optional[str] = None #技能名称
    standardWorkHours: Optional[float] = None #标准工时
    workCycle: Optional[str] = None #任务周期
    startTime: Optional[str] = None #开始时间
    endTime: Optional[str] = None #结束时间
    status: Optional[str] = None #状态
    stationId: Optional[int] = None #站点ID
    taskTypeId: Optional[int] = None #任务类型ID
    workCycleType: Optional[int] = None #任务周期类型
    cycleInterval: Optional[int] = None #任务周期间隔

class GetAiSchedulingEmployeeWorkDayRequest(BaseModel):
    configEmployeeWorkDayId: Optional[int] = None #配置员工工作日ID
    workDay: Optional[str] = None #工作日
    startTime: Optional[str] = None #开始时间
    endTime: Optional[str] = None #结束时间
    status: Optional[str] = None #状态
    configEmployeeId: Optional[int] = None #配置员工ID
    employeeId: Optional[int] = None #员工ID

class GetDispatchSchedulingPlanAiConfigEmployeeRequest(BaseModel):
    schedulingPlanEmployeeId: Optional[int] = None #调度排班计划员工ID
    employeeId: Optional[int] = None #员工ID
    employeeName: Optional[str] = None #员工名称
    postion: Optional[str] = None #职位
    stationId: Optional[int] = None #站点ID
    postionName: Optional[str] = None #职位名称
    skills: Optional[str] = None #技能
    skillNames: Optional[str] = None #技能名称
    type: Optional[str] = None #类型

class GetAiSchedulingEmployeeRequest(BaseModel):
    dispatchSchedulingPlanAiConfigEmployeeVO: Optional[GetDispatchSchedulingPlanAiConfigEmployeeRequest] = None #调度排班计划AI配置员工VO
    dispatchSchedulingPlanAiConfigEmployeeWorkDays: Optional[list[GetAiSchedulingEmployeeWorkDayRequest]] = None #调度排班计划AI配置员工工作日

class GetAiSchedulingAiConfigTeamsRequest(BaseModel):
    name: Optional[str] = None #名称

class GetAiSchedulingGroupRequest(BaseModel):
    dispatchSchedulingPlanAiConfigTeams: Optional[GetAiSchedulingAiConfigTeamsRequest] = None #班组
    dispatchSchedulingPlanAiConfigEmployeeWorkDays: Optional[list[GetAiSchedulingEmployeeWorkDayRequest]] = None #调度排班计划AI配置员工工作日
    dispatchSchedulingPlanAiConfigEmployeeVOs: Optional[list[GetAiSchedulingEmployeeRequest]] = None #调度排班计划AI配置员工VO列表


class GetAiSchedulingTaskRecordRequest(BaseModel):
    workCycleList: Optional[list[GetAiSchedulingTaskWorkCycleRequest]] = None #任务周期列表
    dispatchSchedulingPlanAIConfigTaskType: Optional[GetAiSchedulingTaskRequest] = None #调度排班计划AI配置任务类型
    

class GetAiSchedulingWeekResultRequest(BaseModel):
    task_record_list: Optional[list[GetAiSchedulingTaskRecordRequest]] = None #任务记录列表
    employee_list: Optional[list[GetAiSchedulingEmployeeRequest]] = None #员工列表
    # 班组列表
    group_list: Optional[list[GetAiSchedulingGroupRequest]] = None
    user_input: Optional[str] = None #用户输入
    scheduling_date: Optional[datetime] = None #调度日期


def llm_process(user_input: str, system_prompt: str = None, history: str = None, model: str = None, tools=None, has_history: bool = True, session_id: int = None, template_type: str = None, enable_thinking: bool = False):
    """
    处理用户输入并通过LLM模型生成分析结果
    """
    client = OpenAI(
        api_key=config.env_vars.get("OPENAI_API_KEY"),
        base_url=config.env_vars.get("OPENAI_BASE_URL")
    )
    model = model or config.env_vars.get("OPENAI_MODEL_REASONING")
    messages = []
    if system_prompt:
        messages.append(
            {'role': 'system', 'content': system_prompt},
        )


    if has_history and session_id is not None:
        messages.extend(get_conversation_history(session_id, True, 2))
        # 插入消息
        insert_conversation_message(session_id, "user", user_input)
    if history:
        messages.extend(history)
    messages.append({
        'role': 'user',
        'content': user_input,
    })
    insert_content, insert_reasoning_content = "", ""
    for retry in range(MAX_RETRIES):
        try:
            if enable_thinking:
                resp = client.chat.completions.create(
                    model=model,
                    messages=messages,
                    seed=42,
                    stream=True,
                    tools=tools,
                    tool_choice="auto" if tools else "none",
                    temperature=0.6,
                    top_p=0.95,
                    max_completion_tokens=2048,
                    extra_body={"enable_thinking": enable_thinking, "top_k": 20},
                )
            else:
                resp = client.chat.completions.create(
                    model=model,
                    messages=messages,
                    seed=42,
                    stream=True,
                    tools=tools,
                    tool_choice="auto" if tools else "none",
                    temperature=0.7,
                    top_p=0.8,
                    max_completion_tokens=2048,
                    extra_body={"enable_thinking": enable_thinking, "top_k": 20},
                )
            for chunk in resp:
                if chunk and chunk.choices:
                    delta = chunk.choices[0].delta.content
                    try:
                        reasoning_content = chunk.choices[0].delta.reasoning_content
                    except:
                        reasoning_content = ""
                    tool_calls = chunk.choices[0].delta.tool_calls
                    if tool_calls:
                        print(tool_calls)
                        """
                        [ChoiceDeltaToolCall(index=None, id='1a5e864736692000', function=ChoiceDeltaToolCallFunction(arguments='{"task_id": "1719", "old_executor_id": "42", "new_executor_id": "39"}', name='modify_task_executor'), type='function'), ChoiceDeltaToolCall(index=None, id='1a5e864736692001', function=ChoiceDeltaToolCallFunction(arguments='{"task_id": "1720", "old_executor_id": "42", "new_executor_id": "41"}', name='modify_task_executor'), type='function')]
                        """
                        for tool_call in tool_calls:
                            tool_name = tool_call.function.name
                            tool_arguments = tool_call.function.arguments
                            result, msg = eval(tool_name)(
                                **json.loads(tool_arguments))
                            # arguments_value = ""
                            # for key, value in json.loads(tool_arguments).items():
                            #     arguments_value += f"{key}: {value}\n"
                            # result_value = ""
                            # for key, value in result.items():
                            #     result_value += f"{key}: {value}\n"
                            msg_str = msg + ":" + result["msg"]
                            print(msg_str)
                            yield msg_str, None
                    if delta:
                        insert_content += delta
                    if reasoning_content:
                        insert_reasoning_content += reasoning_content
                    yield delta, reasoning_content
            break
        except Exception as e:
            print(e)
            traceback.print_exc()
        finally:
            insert_conversation_message(
                session_id, "assistant", insert_content, insert_reasoning_content)


def extract_employee_info(user_input: str) -> str:
    """
    提取执行人员信息

    Args:
        user_input (str): 用户输入

    Returns:
        str: 执行人员信息，如果处理失败则返回None
    """
    # 使用配置实例中的环境变量
    client = OpenAI(
        api_key=config.env_vars.get("OPENAI_API_KEY"),
        base_url=config.env_vars.get("OPENAI_BASE_URL")
    )
    model = config.env_vars.get("OPENAI_MODEL")

    messages = [
        # {'role': 'system', 'content': SYSTEM['task_scheduling_intent_extractor_system_prompt_2']},
        {
            'role': 'user',
            'content': SYSTEM['task_scheduling_intent_extractor_system_prompt_2'] + "\n" + user_input,
        }
    ]

    for retry in range(MAX_RETRIES):
        try:
            resp = client.chat.completions.create(
                model=model,
                messages=messages,
                seed=42,
            )
            response_text = resp.choices[0].message.content
            print(333, response_text)
            return response_text
        except Exception as e:
            print(e)

    return None


def get_methodology(type: str, model: str = None) -> str:
    """
    获取方法学
    """
    # 使用配置实例中的环境变量
    client = OpenAI(
        api_key=config.env_vars.get("OPENAI_API_KEY"),
        base_url=config.env_vars.get("OPENAI_BASE_URL")
    )
    model = config.env_vars.get("OPENAI_MODEL_REASONING")

    messages = [
        {'role': 'user',
            'content': SYSTEM['task_scheduling_methodology_system_prompt_ds'].format(type)},
    ]
    for retry in range(MAX_RETRIES):
        try:
            resp = client.chat.completions.create(
                model=model,
                messages=messages,
                seed=42,
                stream=True,
            )
            for chunk in resp:
                if chunk and chunk.choices:
                    delta = chunk.choices[0].delta.content
                    try:
                        reasoning_content = chunk.choices[0].delta.reasoning_content
                    except:
                        reasoning_content = ""
                if delta or reasoning_content:
                    # logging.info(f"获得响应: {resp}")
                    yield delta, reasoning_content  # 逐步返回响应内容
            break
        except Exception as e:
            print(e)
            traceback.print_exc()


@app.post("/get_ai_dispatch_task")
def get_ai_dispatch_task(request: AIDispatchTaskRequest):
    user_input = request.user_input
    employee_list = request.employee_list
    task_info = request.task_info
    history = request.history

    if not user_input:
        raise HTTPException(status_code=400, detail="缺少必要参数")

    employee_str = ""
    for employee in employee_list:
        employee_str += f"""
        姓名：{employee.get('employeeName', '未知')}
        员工编号：{employee.get('employeeId', '未知')}
        技能：{employee.get('skillNames', '未知')}
        当日剩余任务数：{employee.get('remainingTaskCount', '未知')}
        总任务数：{employee.get('totalTaskCount', '未知')}
        状态：{'空闲中' if employee.get('employeeStatus', '0') == '0' else '忙碌中'}
        当前位置：{employee.get('currentLocation', '未知')}
        当前任务：{employee.get('currentTaskName', '未知')}
        执行中任务开始时间：{employee.get('taskBeginTime', '')}
        执行中任务结束时间：{employee.get('taskEndTime', '')}
        """

    user_input = f"""
    # 用户输入

    {user_input}

    ## 突发任务要求

    任务名称：{task_info.get('task_name', '未知')}
    技能：{task_info.get('skill', '未知')}
    任务描述：{task_info.get('task_description', '未知')}
    任务开始时间: {task_info.get('task_begin_time', '未知')}
    任务结束时间: {task_info.get('task_end_time', '未知')}
    ## 要求
    1. 请不要指派任务给忙碌中的人员

    ## 人员信息

    {employee_str}

    ## 变量
    当前时间：{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
    
    请不要输出思考过程，直接给出派工结果, 返回示例：
    任务分配给了<员工姓名>， 员工编号是<员工编号>
    """

    answer = ""
    for chunk, reasoning_content in llm_process(user_input=user_input, system_prompt=SYSTEM['task_scheduling_burst_tasks_system_prompt'], history=history, model=config.env_vars.get("OPENAI_MODEL")):
        if chunk:
            print(chunk, end="")
            answer += chunk
    answer = extract_employee_info(answer)
    extract_response = {"code": "500", "msg": "", "data": ""}
    if "```json" in answer and "employee_name" in answer:
        answer = json.loads(answer.split("```json")[1].split("```")[0])
        extract_response = {"code": "200", "msg": "", "data": answer}

    return JSONResponse(content=extract_response)


def get_burst_tasks_event_result(request: BurstTasksRequest):
    user_input = request.user_input
    employee_list = request.employee_list
    task_info = request.task_info

    if not user_input:
        raise HTTPException(status_code=400, detail="缺少必要参数")

    answer = ""
    methodology_answer = ""
    # for chunk, reasoning_content in get_methodology(type="任务派工"):
    #     if chunk or reasoning_content:
    #         print(chunk or reasoning_content, end="")
    #         if chunk:
    #             methodology_answer += chunk
    #         response = json.dumps(
    #             {"status": "success", "response": None, "reasoning_content": reasoning_content or chunk}, ensure_ascii=False)
    #         yield response
    methodology_answer = """
    线性规划
    整数规划
    遗传算法
    约束满足问题（CSP）
    动态规划
    模拟退火算法
    """
    if methodology_answer:
        methodology_answer = "## 方法学\n" + methodology_answer

    employee_str = ""
    for employee in employee_list:
        employee_str += f"""
        姓名：{employee.get('employeeName', '未知')}
        员工编号：{employee.get('employeeId', '未知')}
        技能：{employee.get('skillNames', '未知')}
        当日剩余任务数：{employee.get('remainingTaskCount', '未知')}
        总任务数：{employee.get('totalTaskCount', '未知')}
        状态：{'空闲中' if employee.get('employeeStatus', '0') == '0' else '忙碌中'}
        当前位置：{employee.get('currentLocation', '未知')}
        当前任务：{employee.get('currentTaskName', '未知')}
        当前位置距离任务地点距离：{str(employee.get('distance', '未知')) + "km" if employee.get('distance', '未知') is not None else '未知'}
        """

    user_input = f"""
    # 用户输入

    {user_input}

    ## 突发任务要求

    任务名称：{task_info.get('task_name', '未知')}
    技能：{task_info.get('skill', '未知')}
    任务描述：{task_info.get('task_description', '未知')}
    任务开始时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
    任务结束时间: {task_info.get('taskEndTime', '未知')}
    完成任务需要的标准工时（小时）：{task_info.get('standardHours', '未知')}
    任务站点名称：{task_info.get('stationName', '未知')}
    ## 人员信息

    {employee_str}

    {methodology_answer}

    请不要输出思考过程，直接给出派工思路和派工结果
    """

    for chunk, reasoning_content in llm_process(user_input=user_input, system_prompt=SYSTEM['task_scheduling_burst_tasks_system_prompt_ds'], history=[], enable_thinking=True):
        if chunk or reasoning_content:
            print(chunk or reasoning_content, end="")
            if chunk:
                answer += chunk
            if chunk:
                for chunk_ in chunk:
                    response = json.dumps(
                        {"status": "success", "response": chunk_, "reasoning_content": reasoning_content}, ensure_ascii=False)
                    yield response
                    time.sleep(0.02)
            elif reasoning_content:
                response = json.dumps(
                    {"status": "success", "response": chunk, "reasoning_content": reasoning_content}, ensure_ascii=False)
                yield response
    answer = extract_employee_info(answer)
    if "```json" in answer and "employee_name" in answer:
        answer = json.loads(answer.split("```json")[1].split("```")[0])
        extract_response = json.dumps(
            {"status": "success", "response": answer}, ensure_ascii=False)
        yield extract_response


@app.post("/get_burst_tasks_result")
async def get_burst_tasks_result(request: BurstTasksRequest):
    return StreamingResponse(get_burst_tasks_event_result(request), media_type="text/event-stream")


def get_routine_tasks_event_result(request: RoutineTasksRequest):
    task_record_list = request.task_record_list
    employee_list = request.employee_list
    rule_list = request.rule_list
    history = request.history or []
    if not task_record_list:
        raise HTTPException(status_code=400, detail="缺少必要参数")

    employee_info_dict = collections.defaultdict(list)
    for employee_dict in employee_list:
        employee = employee_dict.get('employees', [])
        skill_str = ""
        for employee_ in employee:
            employee_name = employee_.get('employeeName', '未知')
            employee_id = employee_.get('employeeId', '未知')
            for skill in employee_.get('skillList', []):
                employee_info_dict[skill.get(
                    'skillName', '未知')].append(f"{employee_name}（{employee_id}）")
    print(employee_info_dict)
    methodology_answer = """
    线性规划
    整数规划
    遗传算法
    约束满足问题（CSP）
    动态规划
    模拟退火算法
    """
    if methodology_answer:
        methodology_answer = "## 方法学\n" + methodology_answer

    task_info = "# 任务信息\n"
    employee_hour_dict = collections.defaultdict(float)
    task_date = None
    for task_record in task_record_list:
        skill_str = ""
        skill_list = task_record.get('skillVos', [])
        has_skill_employee_list = []
        if skill_list:
            skill_name = skill_list[0].get('skillName', '未知')
            has_skill_employee_list = employee_info_dict.get(skill_name, [])
        employee_hour_dict[task_record.get(
            'employeeId', "未知")] += float(task_record.get('taskDuration', '0'))
        task_begin_time = task_record.get('taskBeginTime', '未知')
        task_end_time = task_record.get('taskEndTime', '未知')
        if task_date is None and task_begin_time != '未知' and task_end_time != '未知':
            task_date = task_begin_time.split("T")[0]
            task_begin_time = task_begin_time.split("T")[1]
            task_end_time = task_end_time.split("T")[1]

        task_info += f"""
        任务： {task_record.get('schedulingRecordId', '未知')}（{task_record.get('taskTypeName', '未知')}）
        开始时间： {task_begin_time}
        结束时间： {task_end_time}
        执行人： {task_record.get('employeeName', '未知')}（{task_record.get('employeeId', '未知')}）
        任务可分配人员有: {",".join(has_skill_employee_list)}
        """

    employee_info = "\n# 员工信息\n"
    for employee_dict in employee_list:
        employee = employee_dict.get('employees', [])
        skill_str = ""
        for skill in employee_dict.get('skillList', []):
            skill_str += f"{skill.get('skillName', '未知')}（{skill.get('skillId', '未知')}）\n"
        for employee_ in employee:
            employee_info += f""" 
            员工：{employee_.get('employeeName', '未知')}（{employee_.get('employeeId', '未知')}）
            员工工作饱和度：{float(employee_.get('saturation', '0') * 100)}%
            员工当日工作时长：{employee_hour_dict.get(employee_.get('employeeId', '未知'), '0')}小时
            """

    user_input = task_info + employee_info + "\n" + methodology_answer + "\n排班规则："
    for rule in rule_list:
        status = rule.get("status", "2")
        if status == "2":
            continue
        rule_type = rule.get("type", "")
        rule_name = rule.get("name", "")
        extField1 = rule.get("extField1", "")
        extField2 = rule.get("extField2", "")
        if rule_type != "" and rule_type == "1":
            user_input += f"\n{rule_name}{extField1}{extField2}"

    user_input += """            
    请根据<排班规则>给出你的优化建议，不要输出思考过程，不要输出全局优化，直接给出只针对员工的优化建议：
    """
    print(user_input)
    assign_content = ""
    for chunk, reasoning_content in llm_process(user_input=user_input, system_prompt=SYSTEM['task_scheduling_routine_tasks_system_prompt_ds'], history=history, enable_thinking=True):
        if chunk or reasoning_content:
            print(chunk or reasoning_content, end="")
        if chunk:
            assign_content += chunk
            for chunk_ in chunk:
                response = json.dumps(
                    {"status": "success", "response": "", "reasoning_content": chunk_ or reasoning_content}, ensure_ascii=False)
                yield response

                time.sleep(0.02)
        elif reasoning_content:
            response = json.dumps(
                {"status": "success", "response": chunk, "reasoning_content": reasoning_content}, ensure_ascii=False)
            yield response
    check_input = SYSTEM['task_scheduling_routine_tasks_check_prompt'].format(
        task_info, employee_info, assign_content)
    check_content = ""
    for chunk, reasoning_content in llm_process(user_input=check_input, system_prompt=SYSTEM['task_scheduling_routine_tasks_check_prompt'], history=history, enable_thinking=True):
        if chunk or reasoning_content:
            print(chunk or reasoning_content, end="")
            if chunk:
                check_content += chunk
                response = json.dumps(
                    {"status": "success", "response": chunk, "reasoning_content": reasoning_content}, ensure_ascii=False)
                yield response
            elif reasoning_content:
                response = json.dumps(
                    {"status": "success", "response": chunk, "reasoning_content": reasoning_content}, ensure_ascii=False)
                yield response
            time.sleep(0.02)

    history.append({"role": "user", "content": user_input})
    history.append({"role": "assistant", "content": check_content})
    yield json.dumps({"status": "success", "response": "", "history": history}, ensure_ascii=False)


@app.post("/get_routine_tasks_result")
async def get_routine_tasks_result(request: RoutineTasksRequest):
    return StreamingResponse(get_routine_tasks_event_result(request), media_type="text/event-stream")


def get_optimize_routine_tasks_event_result(request: OptimizeRoutineTasksRequest):
    task_record_list = request.task_record_list
    employee_list = request.employee_list
    select_option = request.select_option

    task_data = None
    for task_record in task_record_list:
        task_data = task_record.get('taskBeginTime', '未知')
        if task_data is not None and task_data != '未知':
            task_data = task_data.split("T")[0]
    if task_data is None:
        raise HTTPException(status_code=400, detail="缺少必要参数")
    user_input = "\n调整内容："
    for index, select_option in enumerate(select_option):
        user_input += f"\n{index}. {select_option}"
    user_input += f"""
    请根据<调整内容>直接修改任务。
    任务日期：{task_data}
    """
    print(user_input)
    print(config.env_vars.get("OPENAI_MODEL_TOOL"))
    for chunk, reasoning_content in llm_process(user_input=user_input, tools=get_tools(), model=config.env_vars.get("OPENAI_MODEL_TOOL")):
        if chunk or reasoning_content:
            print(chunk or reasoning_content, end="")
        if chunk:
            yield json.dumps(
                {"status": "success", "response": chunk, "reasoning_content": reasoning_content}, ensure_ascii=False)


# 优化例行任务排班
@app.post("/optimize_routine_tasks")
async def optimize_routine_tasks(request: OptimizeRoutineTasksRequest):
    return StreamingResponse(get_optimize_routine_tasks_event_result(request), media_type="text/event-stream")


def get_burst_tasks_event_result_by_delete(request: BurstTasksRequest):
    user_input = request.user_input
    employee_list = request.employee_list
    task_info = request.task_info

    if not user_input:
        raise HTTPException(status_code=400, detail="缺少必要参数")

    answer = ""
    methodology_answer = ""
    # for chunk, reasoning_content in get_methodology(type="任务派工", model=config.env_vars.get("OPENAI_MODEL")):
    #     if chunk or reasoning_content:
    #         print(chunk or reasoning_content, end="")
    #         if chunk:
    #             methodology_answer += chunk
    methodology_answer = """
    线性规划
    整数规划
    遗传算法
    约束满足问题（CSP）
    动态规划
    模拟退火算法
    """
    if methodology_answer:
        methodology_answer = "## 方法学\n" + methodology_answer

    employee_str = ""
    for employee in employee_list:
        employee_str += f"""
        姓名：{employee.get('employeeName', '未知')}
        员工编号：{employee.get('employeeId', '未知')}
        技能：{employee.get('skillNames', '未知')}
        当日剩余任务数：{employee.get('remainingTaskCount', '未知')}
        总任务数：{employee.get('totalTaskCount', '未知')}
        状态：{'空闲中' if employee.get('employeeStatus', '0') == '0' else '忙碌中'}
        当前位置：{employee.get('currentLocation', '未知')}
        当前任务：{employee.get('currentTaskName', '未知')}
        当前位置距离任务地点距离：{str(employee.get('distance', '未知')) + "km" if employee.get('distance', '未知') is not None else '未知'}
        """

    user_input = f"""
    # 用户输入

    {user_input}

    ## 突发任务要求

    任务名称：{task_info.get('task_name', '未知')}
    技能：{task_info.get('skill', '未知')}
    任务描述：{task_info.get('task_description', '未知')}
    任务开始时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
    任务结束时间: {task_info.get('taskEndTime', '未知')}
    完成任务需要的标准工时（小时）：{task_info.get('standardHours', '未知')}
    任务站点名称：{task_info.get('stationName', '未知')}
    ## 人员信息

    {employee_str}

    {methodology_answer}

    请不要输出思考过程，直接给出结果
    """

    for chunk, reasoning_content in llm_process(user_input=user_input, system_prompt=SYSTEM['task_scheduling_burst_tasks_result_by_delete_system_prompt_ds'], history=[], model=config.env_vars.get("OPENAI_MODEL")):
        if chunk or reasoning_content:
            print(chunk or reasoning_content, end="")
            if chunk:
                answer += chunk
    return {"status": "success", "response": answer}


@app.post("/get_burst_tasks_result_by_delete")
async def get_burst_tasks_result_by_delete(request: BurstTasksRequest):
    return get_burst_tasks_event_result_by_delete(request)


@app.post("/get_standard_hour")
def get_standard_hour(request: GetStandardHourRequest):
    user_input = request.user_input

    answer = ""
    for chunk, reasoning_content in llm_process(user_input=user_input, system_prompt=SYSTEM['task_scheduling_get_standard_hour_prompt'], history=[], model=config.env_vars.get("OPENAI_MODEL")):
        if chunk:
            print(chunk, end="")
            answer += chunk
    extract_response = {"code": "500", "msg": "", "data": ""}
    if "```json" in answer and "hour" in answer:
        answer = json.loads(answer.split("```json")[1].split("```")[0])
        extract_response = {"code": "200", "msg": "", "data": answer}

    return JSONResponse(content=extract_response)


def get_abnormal_alarm_analysis_event_result(request: GetAbnormalAlarmAnalysisRequest):
    user_input = request.user_input
    alarm_id = request.alarm_id
    alarm_detail = request.alarm_detail
    alarm_time = request.alarm_time
    device_id = request.device_id
    # 获取alarm_time 前一个小时的数据
    alarm_time = datetime.strptime(alarm_time, "%Y-%m-%d %H:%M:%S")
    begin_time = alarm_time - timedelta(hours=1)
    begin_time = begin_time.strftime("%Y-%m-%d %H:%M:%S")
    end_time = alarm_time.strftime("%Y-%m-%d %H:%M:%S")
    alarm_real_time_data = get_alarm_real_time_data(
        begin_time, end_time, alarm_id, device_id)
    real_time_str = ""
    for real_time_item in alarm_real_time_data.get("data", []):
        name = real_time_item.get("name", None)
        if not name:
            continue
        real_time_str += f"设备名称: {name}\n"
        for children in real_time_item.get("children", []):
            monitor_time = children.get("monitor_time", "")
            point_value = children.get("point_value", "")
            real_time_str += f"""
            时间：{monitor_time}
            值: {point_value}
            """
    system_prompt = SYSTEM['task_scheduling_get_abnormal_alarm_analysis_prompt'].format(
        alarm_id, alarm_detail, real_time_str)
    print(system_prompt)
    for chunk, reasoning_content in llm_process(user_input=user_input, system_prompt=system_prompt, history=[], model=config.env_vars.get("OPENAI_MODEL_REASONING"), has_history=True, template_type="abnormal_alarm_analysis", session_id=request.session_id, enable_thinking=True):
        if chunk or reasoning_content:
            print(chunk or reasoning_content, end="")
            yield json.dumps(
                {"status": "success", "response": chunk, "reasoning_content": reasoning_content}, ensure_ascii=False)
    return {"status": "success", "response": ""}

# 获取异常报警分析


@app.post("/get_abnormal_alarm_analysis")
async def get_abnormal_alarm_analysis(request: GetAbnormalAlarmAnalysisRequest):
    return StreamingResponse(get_abnormal_alarm_analysis_event_result(request), media_type="text/event-stream")


# 获取历史记录
@app.post("/get_history")
async def get_history(request: GetHistoryRequest):
    return {"status": "success", "data": get_conversation_history(request.session_id, True)}


# 获取历史记录


@app.post("/get_session_list")
async def get_session_list(request: GetSessionRequest):
    return {"status": "success", "data": get_conversation_session(request.template_type)}


def get_ai_scheduling_week_result_event_result(request: GetAiSchedulingWeekResultRequest):
    scheduling_date = request.scheduling_date
    if not scheduling_date:
        scheduling_date = datetime.now()
    week_day = scheduling_date.weekday() + 1
    check_answer = ""
    task_list = assemble_ai_dispatch_task_data(request.task_record_list, scheduling_date)
    employee_list = assemble_ai_dispatch_employee_data(request.employee_list, request.group_list)
    if len(employee_list) == 0 or len(task_list) == 0:
        yield json.dumps({"status": "success", "response": "当前任务和人员信息不足以进行AI排班，请提供有效的人员和任务！"}, ensure_ascii=False) + "\n#{%}"
        return
    result = []
    error_count = 0
    while error_count < 2:
        try:
            for _ in range(1, 2):
                weekday = week_day
                session_id = str(random.randint(1000000000000000000, 9999999999999999999))
                for _ in range(2):
                    employee_list_by_weekday = filter_employee_by_weekday(employee_list, weekday)
                    paiban_result = ""
                    if len(employee_list_by_weekday) == 0:
                        yield json.dumps({"status": "success", "response": f"周{weekday}的人员信息不足以进行AI排班！"}, ensure_ascii=False) + "\n#{%}"
                        break
                    system_prompt = SYSTEM['ai_scheduling_week_system_prompt'].format(employee_list_by_weekday, task_list)
                    history = []
                    if check_answer:
                        history.append({
                            'role': 'user',
                            'content': f"用户反馈内容 \n {check_answer}",
                        })
                    print(f"周{weekday}的排班表")
                    user_input = f" \n  请返回给我周{weekday}的排班表，排班信息中的《人员执行任务时间》要确切的开始时间~结束时间，请确保同一个人同一时间段不能执行两个任务，请确保同一个人在不同任务的《人员执行任务时间》不能重叠，只需要返回排班信息，不要返回别的内容, 不要虚构人员和任务"
                    for chunk, reasoning_content in llm_process(user_input=user_input, system_prompt=system_prompt, history=history, model=config.env_vars.get("OPENAI_MODEL"), session_id=session_id):
                        if chunk or reasoning_content:
                            yield json.dumps(
                                {"status": "success", "response": chunk, "reasoning_content": reasoning_content}, ensure_ascii=False) + "\n#{%}"
                            print(chunk or reasoning_content, end="")
                        if chunk:
                            paiban_result += chunk
                    user_input = SYSTEM['ai_scheduling_week_check_prompt']
                    check_answer = ""
                    for chunk, reasoning_content in llm_process(user_input=user_input, system_prompt=None, history=[], model=config.env_vars.get("OPENAI_MODEL"), session_id=session_id):
                        if chunk or reasoning_content:
                            yield json.dumps(
                                {"status": "success", "response": chunk, "reasoning_content": reasoning_content}, ensure_ascii=False) + "\n#{%}"
                        if chunk:
                            check_answer += chunk
                        print(chunk or reasoning_content, end="")
                    if "成功" in check_answer:
                        break
                yield json.dumps({"status": "success", "response": paiban_result, "is_answer": True, "weekday": weekday}, ensure_ascii=False) + "\n#{%}"
                if "```json" in paiban_result:
                    result.append(json.loads(paiban_result.split("```json")[1].split("```")[0]))
            print(convert_ai_dispatch_task_field_dict_to_dict(result))
            yield json.dumps({"status": "success", "response": convert_ai_dispatch_task_field_dict_to_dict(result), "is_answer": True, "weekday": "all"}, ensure_ascii=False) + "\n#{%}"
            break
        except Exception as e:
            error_count += 1
            print(e)
            continue
# AI一键排班
@app.post("/get_ai_scheduling_week_result")
async def get_ai_scheduling_week_result(request: GetAiSchedulingWeekResultRequest):
    return StreamingResponse(get_ai_scheduling_week_result_event_result(request), media_type="text/event-stream")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("llms.llm_task_scheduling_api_server:app", host="0.0.0.0", port=18800,
                 reload=True, workers=16)