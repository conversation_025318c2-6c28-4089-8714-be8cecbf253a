import cv2
import numpy as np
import argparse
from pathlib import Path
import configparser

def load_transform_params(params_file):
    """从参数文件加载透视变换参数"""
    config = configparser.ConfigParser()
    config.read(params_file, encoding='utf-8')
    
    points = {}
    
    for section in ['top', 'bottom', 'horizon']:
        points[section] = []
        for key in ['point1', 'point2']:
            coords = config[section][key].split(',')
            points[section].append((int(coords[0]), int(coords[1])))
    
    # 加载图像尺寸（如果存在）
    reference_size = None
    if 'image_size' in config:
        if 'width' in config['image_size'] and 'height' in config['image_size']:
            width = int(config['image_size']['width'])
            height = int(config['image_size']['height'])
            reference_size = (height, width)
    
    return points, reference_size

def apply_perspective_transform(image, points):
    """应用透视变换到图像"""
    # 获取四个角点
    top_left = points['top'][0]
    top_right = points['top'][1]
    bottom_left = points['bottom'][0]
    bottom_right = points['bottom'][1]
    
    # 源点
    src_points = np.float32([
        top_left, top_right,
        bottom_left, bottom_right
    ])
    
    # 计算目标尺寸
    target_width = int(np.sqrt(
        (points['horizon'][1][0] - points['horizon'][0][0])**2 +
        (points['horizon'][1][1] - points['horizon'][0][1])**2
    ))
    
    left_height = int(np.sqrt(
        (bottom_left[0] - top_left[0])**2 +
        (bottom_left[1] - top_left[1])**2
    ))
    right_height = int(np.sqrt(
        (bottom_right[0] - top_right[0])**2 +
        (bottom_right[1] - top_right[1])**2
    ))
    target_height = int((left_height + right_height) / 2)
    
    # 目标点
    dst_points = np.float32([
        [0, 0],
        [target_width, 0],
        [0, target_height],
        [target_width, target_height]
    ])
    
    # 计算变换矩阵
    matrix = cv2.getPerspectiveTransform(src_points, dst_points)
    
    # 执行变换
    result = cv2.warpPerspective(image, matrix, (target_width, target_height))
    
    return result

def scale_points_for_image(reference_points, reference_size, target_size):
    """根据图像尺寸调整变换参数点坐标"""
    scale_x = target_size[1] / reference_size[1]
    scale_y = target_size[0] / reference_size[0]
    
    scaled_points = {}
    for key, points in reference_points.items():
        scaled_points[key] = [(int(p[0] * scale_x), int(p[1] * scale_y)) for p in points]
    
    return scaled_points

def process_image(image_path, params_file, reference_size=None, output_path=None):
    """处理单张图像
    
    Args:
        image_path: 图像文件路径或numpy数组
        params_file: 变换参数文件路径
        reference_size: 参考图像尺寸，元组(height, width)
        output_path: 输出图像路径
        
    Returns:
        处理后的图像(numpy数组)
    """
    # 判断输入是图像路径还是numpy数组
    if isinstance(image_path, np.ndarray):
        # 直接使用numpy数组
        image = image_path
    else:
        # 加载图像
        image = cv2.imread(str(image_path))
        if image is None:
            print(f"无法读取图像: {image_path}")
            return None
    
    # 加载变换参数
    points, params_reference_size = load_transform_params(params_file)
    
    # 优先使用命令行参数提供的参考尺寸，如果没有则使用参数文件中的尺寸
    if reference_size is None and params_reference_size is not None:
        reference_size = params_reference_size
        print(f"使用参数文件中的参考尺寸: {reference_size[1]}x{reference_size[0]}")
    
    # 如果提供了参考尺寸，则调整点坐标
    if reference_size:
        points = scale_points_for_image(points, reference_size, image.shape)
    
    # 应用透视变换
    result = apply_perspective_transform(image, points)
    
    # # 保存结果
    if output_path:
        # 检查output_path是否为目录
        output_path = Path(output_path)
        if output_path.is_dir():
            # 如果是目录，则使用原始文件名加上pre
            image_path = Path(image_path)
            output_file = output_path / f"{image_path.stem}_pre{image_path.suffix}"
        else:
            # 如果是文件路径，直接使用
            output_file = output_path
            
        cv2.imwrite(str(output_file), result)
        print(f"处理完成，结果已保存到: {output_file}")
    
    return result

def process_directory(input_dir, params_file, reference_size=None, output_dir=None):
    """批量处理目录中的图像"""
    input_path = Path(input_dir)
    
    # 如果未指定输出目录，则在输入目录下创建 'processed' 文件夹
    if output_dir is None:
        output_path = input_path / 'processed'
    else:
        output_path = Path(output_dir)
        
    output_path.mkdir(exist_ok=True)
    
    # 获取所有图像文件
    image_files = list(input_path.glob('*.jpg')) + list(input_path.glob('*.jpeg')) + \
                 list(input_path.glob('*.png')) + list(input_path.glob('*.bmp'))
    
    if not image_files:
        print(f"目录中未找到图像文件: {input_dir}")
        return
    
    print(f"共找到 {len(image_files)} 张图像，开始处理...")
    
    # 加载变换参数（只需加载一次）
    points, params_reference_size = load_transform_params(params_file)
    
    # 优先使用命令行参数提供的参考尺寸，如果没有则使用参数文件中的尺寸
    if reference_size is None and params_reference_size is not None:
        reference_size = params_reference_size
        print(f"使用参数文件中的参考尺寸: {reference_size[1]}x{reference_size[0]}")
    
    for i, img_path in enumerate(image_files):
        print(f"[{i+1}/{len(image_files)}] 处理: {img_path.name}")
        
        try:
            # 加载图像
            image = cv2.imread(str(img_path))
            if image is None:
                print(f"  - 错误: 无法读取图像")
                continue
                
            # 如果提供了参考尺寸，则调整点坐标
            current_points = points.copy()
            if reference_size:
                current_points = scale_points_for_image(current_points, reference_size, image.shape)
            
            # 应用透视变换
            result = apply_perspective_transform(image, current_points)
            
            # 保存结果
            output_file = output_path / f"{img_path.stem}_pre{img_path.suffix}"
            cv2.imwrite(str(output_file), result)
            print(f"  - 成功: 结果已保存到: {output_file.name}")
            
        except Exception as e:
            print(f"  - 错误: 处理失败 - {str(e)}")
    
    print(f"所有图像处理完成，结果保存在: {output_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='根据保存的变换参数处理图像')
    parser.add_argument('--params', '-p',default='/home/<USER>/llm_project/yolo_project/code/ZL-YOLO/pre_line.txt',required=False, help='变换参数文件路径')
    parser.add_argument('--input', '-i',default='/home/<USER>/llm_project/yolo_project/datasets/竹料/2025-4-27', required=False, help='输入图像或目录路径')
    parser.add_argument('--output', '-o',default='/home/<USER>/llm_project/yolo_project/datasets/竹料/2025-4-27-pre', help='输出图像或目录路径')
    parser.add_argument('--reference_width', type=int, help='参考图像宽度（覆盖参数文件中的设置）')
    parser.add_argument('--reference_height', type=int, help='参考图像高度（覆盖参数文件中的设置）')
    
    args = parser.parse_args()
    
    # 设置参考尺寸
    reference_size = None
    if args.reference_width and args.reference_height:
        reference_size = (args.reference_height, args.reference_width)
    
    # 检查输入是否为有效文件路径
    if isinstance(args.input, str):
        input_path = Path(args.input)
        
        # 处理单个图像或目录
        if input_path.is_file():
            output_path = args.output if args.output else input_path.with_stem(input_path.stem + "_pre")
            process_image(input_path, args.params, reference_size, output_path)
        elif input_path.is_dir():
            process_directory(input_path, args.params, reference_size, args.output)
        else:
            print(f"输入路径无效: {args.input}")
    else:
        # 如果输入不是字符串路径，假设它是numpy数组
        if isinstance(args.input, np.ndarray):
            result = process_image(args.input, args.params, reference_size)
            if args.output and result is not None:
                output_path = Path(args.output)
                cv2.imwrite(str(output_path), result)
                print(f"处理完成，结果已保存到: {args.output}")
            return result
        else:
            print("输入类型无效，应为图像路径、目录路径或numpy数组")

if __name__ == "__main__":
    main() 