"{'任务名称': '巡检', '姓名': '马天龙', '时段': '00:00~23:59', '任务编码': 27, '技能要求': ['操作'], '人员执行任务时间': ['08:00~08:30'], '标准工时': 0.5, '任务周期': '每0小时一次'}"
from datetime import datetime, timedelta
import json

date_str_dict = {
    "1": "周一",
    "2": "周二", 
    "3": "周三",
    "4": "周四",
    "5": "周五",
    "6": "周六",
    "7": "周日"
}
convert_ai_dispatch_task_field_dict = {
    "任务名称": "taskName",
    "姓名": "employeeName",
    "时段": "time",
    "任务编码": "taskId",
    "技能要求": "skillNames",
    "人员执行任务时间": "staffTaskTime",
    "标准工时": "standardWorkHours",
    "任务周期": "workCycle",
    "人员编码": "employeeId"
}
# 转换AI派工的字段对象
def convert_ai_dispatch_task_field(field: str) -> str:
    if field in convert_ai_dispatch_task_field_dict:
        return convert_ai_dispatch_task_field_dict[field]
    return field

def convert_ai_dispatch_task_field_dict_to_dict(field_list: list) -> dict:
    if isinstance(field_list, str):
        field_list = json.loads(field_list)
    result_list = []
    if isinstance(field_list, list):
        for week_data in field_list:
            result = []
            for task_list in week_data.values():
                for task in task_list:
                    result_dict = {}
                    for key in task.keys():
                        result_dict[convert_ai_dispatch_task_field(key)] = task[key]
                    result.append(result_dict)
            result_list.append(result)
    return result_list



# 组装AI派工的任务数据
def assemble_ai_dispatch_task_data(task_record_list: list, scheduling_date: datetime) -> list:
    task_list = []
    for task_record in task_record_list:
        task = task_record.dispatchSchedulingPlanAIConfigTaskType
        range_list = []
        if task_record.workCycleList is None or len(task_record.workCycleList) == 0:
            continue
        if task.workCycle == "周期不固定":
            for work_cycle in task_record.workCycleList:
                begin_time = work_cycle.beginTime
                end_time = work_cycle.endTime
                try:
                    begin_time = datetime.strptime(begin_time, "%Y-%m-%d %H:%M:%S")
                    end_time = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
                    begin_time = begin_time.strftime("%H:%M")
                    end_time = end_time.strftime("%H:%M")
                except Exception as e:
                    ...
                range_list.append(f"{begin_time}~{end_time}")
        else:
            if task.standardWorkHours is None or task.standardWorkHours == 0:
                continue
            begin_time = task_record.workCycleList[0].beginTime
            end_time = task_record.workCycleList[0].endTime
            begin_time = begin_time.split(":")
            end_time = end_time.split(":")
            task_begin_time = scheduling_date.replace(hour=int(begin_time[0]), minute=int(begin_time[1]), second=0, microsecond=0)
            task_end_time = scheduling_date.replace(hour=int(end_time[0]), minute=int(end_time[1]), second=0, microsecond=0)
            standard_work_hours = task.standardWorkHours
            task_cycle_frequency = task.cycleInterval
            duration_hours = (task_end_time - task_begin_time).total_seconds() / 3600
            if duration_hours < standard_work_hours:
                continue
            current_time = task_begin_time
            while current_time < task_end_time:
                current_end_time = current_time + timedelta(hours=standard_work_hours)
                if current_end_time > task_end_time:
                    break
                range_list.append(f"{current_time.strftime('%H:%M')}~{current_end_time.strftime('%H:%M')}")
                current_time = current_time + timedelta(hours=task_cycle_frequency)
        if len(range_list) <= 0:
            continue
        task_list.append({
            "任务编码": task.schedulingPlanTaskId,
            "任务名称": task.taskName,
            "任务执行时间范围": range_list,
            "技能要求": task.skillNames.split(",") if task.skillNames else ['无技能要求']
        })
    return task_list

def assemble_ai_dispatch_employee_data(employee_list: list, group_list: list) -> list:
    result_list = []
    for employee_request in employee_list:
        employee_vo = employee_request.dispatchSchedulingPlanAiConfigEmployeeVO
        employee_work_days: list = employee_request.dispatchSchedulingPlanAiConfigEmployeeWorkDays
        work_day_str = ""
        for day in employee_work_days:
            work_day_str += f"{date_str_dict[day.workDay]}:[{day.startTime}~{day.endTime}] \n"
        result_list.append({
            "人员编码": employee_vo.employeeId,
            "姓名": employee_vo.employeeName,
            "职位": employee_vo.postionName,
            "上班日期": work_day_str,
            "已拥有技能": employee_vo.skillNames
        })
    for teams_group in group_list:
        for teams_employee in teams_group.dispatchSchedulingPlanAiConfigEmployeeVOs:
            employee_vo = teams_employee.dispatchSchedulingPlanAiConfigEmployeeVO
            employee_work_days: list = teams_employee.dispatchSchedulingPlanAiConfigEmployeeWorkDays
            work_day_str = ""
            for day in employee_work_days:
                work_day_str += f"{date_str_dict[day.workDay]}:[{day.startTime}~{day.endTime}] \n"
            result_list.append({
                "人员编码": employee_vo.employeeId,
                "姓名": employee_vo.employeeName,
                "职位": employee_vo.postionName,
                "上班日期": work_day_str,
                "已拥有技能": employee_vo.skillNames
            })
    return result_list

# 根据周几筛选员工
def filter_employee_by_weekday(employee_list: list, weekday: int) -> list:
    return [employee for employee in employee_list if date_str_dict[str(weekday)] in employee["上班日期"] or f"周{weekday}" in employee["上班日期"]]

