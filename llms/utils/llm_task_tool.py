

from llms.utils.opshub_api import update_task_executor, update_task_time


def modify_task_executor(task_id: int, old_executor_id: int, new_executor_id: int):
    """
    修改任务执行人
    @param task_id: 任务ID
    @param old_executor_id: 原执行人
    @param new_executor_id: 新执行人
    """
    msg = f"修改任务执行人: {task_id} 从 {old_executor_id} 到 {new_executor_id}"
    # 正则获取数字
    return update_task_executor(task_id, old_executor_id, new_executor_id), msg


def modify_task_time(task_id: int, executor_id: int, old_start_time: str, new_start_time: str, old_end_time: str, new_end_time: str):
    """
    修改任务时间
    @param task_id: 任务ID
    @param executor_id: 执行人ID
    @param old_start_time: 原开始时间(时分)
    @param new_start_time: 新开始时间(时分)
    @param old_end_time: 原结束时间(时分)
    @param new_end_time: 新结束时间(时分)
    """
    msg = f"修改任务时间: {task_id} 从 {old_start_time} 到 {new_start_time} 和 {old_end_time} 到 {new_end_time}"
    return update_task_time(task_id, executor_id, old_start_time, new_start_time, old_end_time, new_end_time), msg


def get_tools():
    """
    获取工具
    """
    # 修改任务执行人
    result = []
    modify_task_executor_tool = {
        "name": "modify_task_executor",
        "description": "修改任务执行人,转移任务执行人",
        "parameters": {
            "properties": {
                "task_id": {
                    "type": "int",
                    "description": "任务ID"
                },
                "old_executor_id": {
                    "type": "int",
                    "description": "原执行人"
                },
                "new_executor_id": {
                    "type": "int",
                    "description": "新执行人"
                }
            },
            "required": ["task_id", "old_executor_id", "new_executor_id"],
            "type": "object"
        }
    }
    modify_task_time_tool = {
        "name": "modify_task_time",
        "description": "修改任务时间,调整任务时间",
        "parameters": {
            "properties": {
                "task_id": {
                    "type": "int",
                    "description": "任务ID"
                },
                "executor_id": {
                    "type": "int",
                    "description": "执行人ID"
                },
                "old_start_time": {
                    "type": "string",
                    "description": "原开始时间(时:分)"
                },
                "new_start_time": {
                    "type": "string",
                    "description": "新开始时间(时:分)"
                },
                "old_end_time": {
                    "type": "string",
                    "description": "原结束时间(时:分)"
                },
                "new_end_time": {
                    "type": "string",
                    "description": "新结束时间(时:分)"
                }
            },
            "required": ["task_id", "executor_id", "old_start_time", "new_start_time", "old_end_time", "new_end_time"],
            "type": "object"
        }
    }
    result.append({
        "type": "function",
        "function": modify_task_executor_tool
    })
    result.append({
        "type": "function",
        "function": modify_task_time_tool
    })
    return result
