from llms.utils.pg_tool import execute_sql, query_data_by_sql


def get_conversation_session(template_type: str):
    """根据 session_id 和 template_type 获取历史记录"""
    query = """
        SELECT * FROM conversation_session 
        where  template_type = %s
        ORDER BY created_at desc
    """
    return [row for row in query_data_by_sql(query, (template_type,))]


def get_conversation_history(session_id: int, reasoning_content: bool = False, limit=0):
    """根据 session_id 和 template_type 获取历史记录"""
    query = """
        SELECT role, content, reasoning_content FROM conversation_message 
        WHERE session_id = %s
        ORDER BY created_at ASC
    """
    result = [{"role": row[0], "content": row[1], "reasoning_content": row[2]} if reasoning_content else {
        "role": row[0], "content": row[1]} for row in query_data_by_sql(query, (session_id,))]
    if limit and len(result) > limit:
        limit_result = [result[0]]
        limit_result.extend(result[-limit:])
        return [{"role": r["role"], "content": r["content"]} for r in limit_result]
    return result


def insert_conversation_message(session_id: int, role: str, content: str, reasoning_content: str = None, template_type=None):
    """插入消息"""
    query = f"""
    select * from conversation_session where session_id = %s
    """
    _session = query_data_by_sql(query, (session_id,))
    if not _session:
        sql = """
            INSERT INTO conversation_session (session_id, user_id, template_type, title, user_name) 
            VALUES (%s, %s, %s, %s, %s)
        """
        execute_sql(sql, (session_id, 0, template_type, "", ""))
    sql = """
        INSERT INTO conversation_message (session_id, role, content, reasoning_content) 
        VALUES (%s, %s, %s, %s)
    """
    return execute_sql(sql, (session_id, role, content, reasoning_content))
