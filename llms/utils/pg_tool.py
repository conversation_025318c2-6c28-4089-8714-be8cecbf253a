from pathlib import Path

import psycopg2
import yaml
import sys
import os
from config_file import config as config_pg
sys.path.append(os.getcwd())  # 添加当前工作目录到系统路径
# def load_config():
#     """加载配置文件

#     从项目根目录的configs/env.yaml文件中加载数据库配置信息

#     Returns:
#         dict: 包含数据库配置信息的字典
#         None: 加载失败时返回None

#     Raises:
#         Exception: 配置文件读取异常
#     """
#     try:
#         config_path = Path(__file__).parent.parent.parent / "configs" / "env.yaml"
#         with open(config_path, 'r', encoding='utf-8') as f:
#             return yaml.safe_load(f)
#     except Exception as e:
#         print(f"加载配置文件失败: {e}")
#         return None


def connect_db():
    """创建数据库连接

    从配置文件获取数据库连接信息并创建连接

    Returns:
        Connection: PostgreSQL数据库连接对象
        None: 连接失败时返回None

    Raises:
        Exception: 数据库连接异常
    """
    try:
        # config = load_config()
        config = config_pg.env
        if not config:
            raise Exception("无法加载配置文件")

        db_config = config['database']['opshub_ai_postgres']

        conn = psycopg2.connect(
            dbname=db_config['dbname'],
            user=db_config['user'],
            password=db_config['password'],
            host=db_config['host'],
            port=db_config['port']
        )
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None


def query_data_by_sql(sql, params=None):
    """执行原生SQL查询

    Args:
        sql (str): SQL查询语句
        params (tuple/list): SQL参数

    Returns:
        list: 查询结果列表

    Raises:
        Exception: 数据库查询异常
    """
    conn = connect_db()
    if not conn:
        return []
    try:
        cur = conn.cursor()
        cur.execute(sql, params)
        result = cur.fetchall()
        return result
    except Exception as e:
        print(f"执行SQL查询错误: {e}")
        return []
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()


def execute_sql(sql, params=None):
    """执行原生SQL语句

    Args:
        sql (str): SQL语句
        params (tuple/list): SQL参数
    """
    conn = connect_db()
    if not conn:
        return []
    try:
        cur = conn.cursor()
        cur.execute(sql, params)
        conn.commit()
    except Exception as e:
        print(f"执行SQL语句错误: {e}")
        return []
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()


def query_data(table, conditions=None, fields=None, order_by=None, limit=None):
    """执行数据库查询

    Args:
        table (str): 表名
        conditions (dict): 查询条件
        fields (list): 查询字段
        order_by (str): 排序条件
        limit (int): 限制返回记录数

    Returns:
        list: 查询结果列表

    Raises:
        Exception: 数据库查询异常
    """
    conn = connect_db()
    if not conn:
        return []
    try:
        cur = conn.cursor()
        # 构建查询字段
        if fields:
            fields_str = ', '.join(fields)
        else:
            fields_str = '*'
        # 构建查询条件
        query_sql = f"SELECT {fields_str} FROM {table}"
        condition_values = []
        if conditions:
            condition_str = ' AND '.join(
                [f"{key} = %s" for key in conditions.keys()])
            condition_values = list(conditions.values())
            query_sql += f" WHERE {condition_str}"
        # 添加排序
        if order_by:
            query_sql += f" ORDER BY {order_by}"
        # 添加结果数量限制
        if limit:
            query_sql += f" LIMIT {limit}"
        cur.execute(query_sql, condition_values)
        # 获取查询结果
        result = cur.fetchall()
        return result
    except Exception as e:
        print(f"查询数据错误: {e}")
        return []
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()
