-- 创建设备日报表
CREATE TABLE IF NOT EXISTS device_daily_report (
    id SERIAL PRIMARY KEY,                            -- 主键ID
    camera_id VARCHAR(50) NOT NULL,                   -- 设备ID/摄像头ID
    device_name VARCHAR(100),                         -- 设备名称
    report_date DATE NOT NULL,                        -- 报告日期
    normal_count INTEGER DEFAULT 0,                   -- 正常记录数
    warning_count INTEGER DEFAULT 0,                  -- 告警记录数
    transition_count INTEGER DEFAULT 0,               -- 状态转变记录数
    normal_analysis TEXT,                             -- 正常运行分析
    warning_analysis TEXT,                            -- 告警分析
    transition_analysis TEXT,                         -- 状态转变分析
    full_report TEXT,                                 -- 完整报告文本
    raw_data JSONB,                                   -- 原始数据JSON
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,   -- 创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,   -- 更新时间
    submitter VARCHAR(50) DEFAULT 'AI巡检员',         -- 提交人
    report_name VARCHAR(200) GENERATED ALWAYS AS (EXTRACT(YEAR FROM report_date)::text || '年' || EXTRACT(MONTH FROM report_date)::text || '月' || EXTRACT(DAY FROM report_date)::text || '日' || device_name || '巡检日报') STORED, -- 日报名称
    UNIQUE (camera_id, report_date)                   -- 设备ID和日期的唯一约束
);

-- 索引
CREATE INDEX IF NOT EXISTS idx_device_daily_report_camera_id ON device_daily_report (camera_id);
CREATE INDEX IF NOT EXISTS idx_device_daily_report_date ON device_daily_report (report_date);
CREATE INDEX IF NOT EXISTS idx_device_daily_report_device_name ON device_daily_report (device_name);
CREATE INDEX IF NOT EXISTS idx_device_daily_report_report_name ON device_daily_report (report_name); -- 添加日报名称索引用于加速模糊查询

-- 注释
COMMENT ON TABLE device_daily_report IS '设备日报表，存储每个设备每天的运行分析报告';
COMMENT ON COLUMN device_daily_report.id IS '主键ID';
COMMENT ON COLUMN device_daily_report.camera_id IS '设备ID/摄像头ID';
COMMENT ON COLUMN device_daily_report.device_name IS '设备名称';
COMMENT ON COLUMN device_daily_report.report_date IS '报告日期';
COMMENT ON COLUMN device_daily_report.normal_count IS '正常记录数';
COMMENT ON COLUMN device_daily_report.warning_count IS '告警记录数';
COMMENT ON COLUMN device_daily_report.transition_count IS '状态转变记录数';
COMMENT ON COLUMN device_daily_report.normal_analysis IS '正常运行分析文本';
COMMENT ON COLUMN device_daily_report.warning_analysis IS '告警分析文本';
COMMENT ON COLUMN device_daily_report.transition_analysis IS '状态转变分析文本';
COMMENT ON COLUMN device_daily_report.full_report IS '完整报告文本（大模型生成）';
COMMENT ON COLUMN device_daily_report.raw_data IS '原始数据JSON，包括原始的warning_data、non_warning_data和transition_data';
COMMENT ON COLUMN device_daily_report.created_at IS '创建时间';
COMMENT ON COLUMN device_daily_report.updated_at IS '更新时间';
COMMENT ON COLUMN device_daily_report.submitter IS '提交人';
COMMENT ON COLUMN device_daily_report.report_name IS '日报名称，格式为"年月日+设备名称+巡检日报"';

-- 为已存在的表添加report_name列（如果表已经存在且需要更新）
ALTER TABLE device_daily_report ADD COLUMN IF NOT EXISTS report_name VARCHAR(200) 
GENERATED ALWAYS AS (EXTRACT(YEAR FROM report_date)::text || '年' || 
                    EXTRACT(MONTH FROM report_date)::text || '月' || 
                    EXTRACT(DAY FROM report_date)::text || '日' || 
                    device_name || '巡检日报') STORED;

-- 为已存在的表添加submitter列（如果表已经存在且需要更新）
ALTER TABLE device_daily_report ADD COLUMN IF NOT EXISTS submitter VARCHAR(50) DEFAULT 'AI巡检员'; 