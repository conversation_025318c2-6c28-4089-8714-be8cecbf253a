# 使用Python作为基础镜像
FROM python:3.10.16
# 设置工作目录
WORKDIR /config
WORKDIR /app
# 复制应用代码到容器中
# COPY ./src /app/src
# COPY ./dex-manage.py /app
COPY ./llm_api_requirements.txt /app
# pip镜像源
RUN pip3 config set global.index-url http://mirrors.aliyun.com/pypi/simple
RUN pip3 config set install.trusted-host mirrors.aliyun.com
# 安装依赖项
RUN pip install --no-cache-dir -r /app/llm_api_requirements.txt
# 暴露应用端口
EXPOSE 18800
#定义时区参数
ENV TZ=Asia/Shanghai
#设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo '$TZ' > /etc/timezone
#设置编码
ENV LANG C.UTF-8
# 设置启动命令
CMD ["python", "-m", "llms.llm_task_scheduling_api_server"]