# 使用Python作为基础镜像
# FROM python:3.10.15
FROM pytorch/pytorch:2.5.0-cuda12.1-cudnn9-devel
# 工作目录已在docker-compose.yml中设置
# WORKDIR /app

# 安装OpenCV和ffmpeg所需的系统依赖
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    ffmpeg \
    pandoc \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 工作目录设置（构建时需要）
WORKDIR /app

# 只复制requirements.txt到容器中，其他代码通过卷挂载
COPY ./requirements.txt /app/

# 安装依赖项
RUN pip install --no-cache-dir -r /app/requirements.txt

# 时区和编码设置已在docker-compose.yml中配置
# 保留时区设置命令，以确保容器内时间正确
RUN ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo Asia/Shanghai > /etc/timezone
# 启动命令已移至docker-compose.yml文件中