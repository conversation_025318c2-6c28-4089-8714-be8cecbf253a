# 滤池重复报警抑制功能说明

## 功能概述

滤池重复报警抑制功能旨在解决滤池故障可能持续数天不修复，导致同一个任务重复报警的问题。该功能在指定时间窗口内，如果曝气头故障数量保持不变，则只在第一次检测到时报警，后续不再重复报警。

## 功能特点

### 1. 智能报警抑制
- **首次检测**：第一次检测到故障时立即触发报警
- **相同故障数量**：在时间窗口内，如果故障数量不变，抑制重复报警
- **故障数量变化**：当故障数量发生变化时，重新触发报警
- **时间窗口重置**：超出时间窗口后，重新开始报警逻辑

### 2. 状态管理
- **摄像头级别状态跟踪**：每个摄像头独立管理故障状态
- **故障计数**：记录当前检测到的故障数量
- **时间记录**：记录首次检测时间和最后检测时间
- **报警状态**：跟踪是否已触发报警

### 3. 自动恢复
- **故障恢复**：当检测结果恢复正常时，自动重置所有状态
- **状态清理**：确保下次故障检测能正常触发报警

## 配置参数

在 `configs/env.yaml` 中添加了以下配置参数：

```yaml
# 滤池重复报警抑制配置
# true: 启用重复报警抑制，在指定时间窗口内相同故障数量不重复报警
# false: 禁用重复报警抑制，每次检测到故障都会报警
filter_alarm_suppression_enabled: true

# 重复报警抑制时间窗口（单位：秒）
# 在此时间窗口内，如果故障数量不变，则不会重复报警
filter_alarm_suppression_window: 1200  # 默认20分钟（1200秒）
```

## 工作流程

### 1. 故障检测流程
```
检测到曝气头故障
    ↓
检查是否启用抑制功能
    ↓
是否首次检测？
    ├─ 是 → 触发报警，记录状态
    └─ 否 → 检查时间窗口
        ↓
    在时间窗口内？
        ├─ 是 → 检查故障数量是否变化
        │   ├─ 无变化 → 抑制报警
        │   └─ 有变化 → 重新报警，重置时间窗口
        └─ 否 → 重新报警，重置时间窗口
```

### 2. 状态数据结构
```python
filter_fault_state = {
    camera_id: {
        "fault_count": int,              # 当前故障数量
        "first_detection_time": datetime, # 首次检测时间
        "alarm_triggered": bool,         # 是否已触发报警
        "last_detection_time": datetime  # 最后检测时间
    }
}
```

## 使用场景

### 场景1：首次检测到故障
- **输入**：检测到1个曝气头故障
- **行为**：立即触发报警
- **状态**：记录故障数量和检测时间

### 场景2：相同故障持续存在
- **输入**：在20分钟内，仍然检测到1个曝气头故障
- **行为**：抑制报警，不重复通知
- **状态**：更新最后检测时间

### 场景3：故障数量发生变化
- **输入**：在20分钟内，检测到2个曝气头故障
- **行为**：重新触发报警
- **状态**：更新故障数量，重置时间窗口

### 场景4：超出时间窗口
- **输入**：超过20分钟后，仍检测到相同数量故障
- **行为**：重新触发报警
- **状态**：重置时间窗口

### 场景5：故障恢复正常
- **输入**：检测结果显示无故障
- **行为**：不报警，重置所有状态
- **状态**：清空故障记录

## 日志示例

```
2025-08-28 18:02:31 - [INFO] - 摄像头 camera_001 首次检测到滤池故障，故障数量: 1，触发报警
2025-08-28 18:02:31 - [INFO] - 摄像头 camera_001 在抑制窗口内(30.0秒)，故障数量未变化(1)，抑制报警
2025-08-28 18:02:31 - [INFO] - 摄像头 camera_001 故障数量发生变化(从 1 到 2)，重新触发报警
2025-08-28 18:02:31 - [INFO] - 摄像头 camera_001 超出抑制窗口(1250.0秒 > 1200秒)，重新触发报警
2025-08-28 18:02:31 - [INFO] - 摄像头 camera_001 滤池故障已恢复正常，重置报警状态
```

## 注意事项

1. **配置灵活性**：可以通过配置文件启用/禁用功能，调整时间窗口
2. **摄像头独立性**：每个摄像头的状态独立管理，互不影响
3. **兼容性**：与现有报警机制完全兼容，不影响其他功能
4. **性能影响**：状态管理开销很小，不会影响系统性能
5. **故障恢复**：确保故障恢复后能正常重新检测和报警

## 维护建议

1. **定期检查配置**：根据实际运行情况调整时间窗口
2. **监控日志**：关注报警抑制的日志，确保功能正常工作
3. **测试验证**：在配置变更后进行功能测试
4. **状态清理**：系统重启时会自动清理所有状态，无需手动干预
