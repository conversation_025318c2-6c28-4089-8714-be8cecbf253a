import requests
import json
import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..')
sys.path.append(project_root)

# 导入配置
from config_file import config


def get_fastgpt_config():
    """获取 FastGPT 配置信息"""
    fastgpt_config = config.env.get('fastgpt', {})
    return {
        'base_url': fastgpt_config.get('base_url', ''),
        'authorization_token': fastgpt_config.get('authorization_token', ''),
        'default_dataset_id': fastgpt_config.get('default_dataset_id', ''),
        'api_endpoints': fastgpt_config.get('api_endpoints', {})
    }


def build_api_url(endpoint_key):
    """构建完整的 API URL"""
    fastgpt_cfg = get_fastgpt_config()
    base_url = fastgpt_cfg['base_url']
    endpoint = fastgpt_cfg['api_endpoints'].get(endpoint_key, '')
    return f"{base_url}{endpoint}"


def get_default_auth_and_dataset():
    """获取默认的授权令牌和数据集ID"""
    fastgpt_cfg = get_fastgpt_config()
    return fastgpt_cfg['authorization_token'], fastgpt_cfg['default_dataset_id']


def list_dataset_collection_with_config(authorization_token=None, dataset_id=None):
    """
    带配置参数的版本，允许自定义授权令牌和数据集ID
    
    Args:
        authorization_token (str): 授权令牌
        dataset_id (str): 数据集ID
    """
    # 获取 FastGPT 配置
    fastgpt_cfg = get_fastgpt_config()
    
    # API 端点
    url = build_api_url('list_collections')
    
    # 使用提供的令牌或配置文件中的默认值
    auth_token = authorization_token or fastgpt_cfg['authorization_token']
    ds_id = dataset_id or fastgpt_cfg['default_dataset_id']
    
    # 请求头
    headers = {
        'Authorization': f'Bearer {auth_token}',
        'Content-Type': 'application/json'
    }
    
    # 请求体数据
    data = {
        "offset": 0,
        "pageSize": 10,
        "datasetId": ds_id,
        "parentId": None,
        "searchText": ""
    }
    
    try:
        # 发送 POST 请求
        response = requests.post(url, headers=headers, json=data)
        
        # 返回响应对象以便进一步处理
        return response
    
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def create_text_collection(authorization_token=None, dataset_id=None, text_content="", collection_name="测试训练"):
    """
    创建文本数据集集合的API请求
    
    Args:
        authorization_token (str): 授权令牌
        dataset_id (str): 数据集ID
        text_content (str): 要添加的文本内容
        collection_name (str): 集合名称
    """
    # 获取 FastGPT 配置
    fastgpt_cfg = get_fastgpt_config()
    
    # API 端点
    url = build_api_url('create_text_collection')
    
    # 使用提供的令牌或配置文件中的默认值
    auth_token = authorization_token or fastgpt_cfg['authorization_token']
    ds_id = dataset_id or fastgpt_cfg['default_dataset_id']
    
    # 请求头
    headers = {
        'Authorization': f'Bearer {auth_token}',
        'Content-Type': 'application/json'
    }
    
    # 请求体数据
    data = {
        "text": text_content,
        "datasetId": ds_id,
        "parentId": None,
        "name": collection_name,
        "trainingType": "qa",
        "chunkSettingMode": "auto",
        "qaPrompt": "",
        "metadata": {}
    }
    
    try:
        # 发送 POST 请求
        response = requests.post(url, headers=headers, json=data)
        
        # 返回响应对象以便进一步处理
        return response
    
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None
def push_data_to_collection(authorization_token=None, collection_id=None, data_list=None, training_type="chunk"):
    """
    为集合批量添加数据（自动分块）
    
    Args:
        authorization_token (str): 授权令牌
        collection_id (str): 集合ID
        data_list (list): 数据列表，格式：[{"q": "问题", "a": "答案"}, ...]
        training_type (str): 数据处理方式，chunk: 按文本长度进行分割; qa: 问答对提取
    """
    # 获取 FastGPT 配置
    fastgpt_cfg = get_fastgpt_config()
    
    # API 端点
    url = build_api_url('push_data')
    
    # 使用提供的令牌或配置文件中的默认值
    auth_token = authorization_token or fastgpt_cfg['authorization_token']
    
    # 请求头
    headers = {
        'Authorization': f'Bearer {auth_token}',
        'Content-Type': 'application/json'
    }
    
    # 请求体数据
    data = {
        "collectionId": collection_id,
        "trainingType": training_type,
        "data": data_list or []
    }
    
    try:
        # 发送 POST 请求
        response = requests.post(url, headers=headers, json=data)
        return response
    
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def create_text_collection_with_chunks(authorization_token=None, dataset_id=None, text_content="", 
                                      collection_name="文本分块集合", chunk_size=1500, training_type="chunk", 
                                      auto_chunk=True):
    """
    创建文本集合并自动分块
    
    Args:
        authorization_token (str): 授权令牌
        dataset_id (str): 数据集ID
        text_content (str): 要添加的文本内容
        collection_name (str): 集合名称
        chunk_size (int): 分块大小（仅在auto_chunk=False时生效）
        training_type (str): 训练类型，chunk 或 qa
        auto_chunk (bool): 是否使用自动分块，True=自动，False=自定义
    """
    # 获取 FastGPT 配置
    fastgpt_cfg = get_fastgpt_config()
    
    # API 端点
    url = build_api_url('create_text_collection')
    
    # 使用提供的令牌或配置文件中的默认值
    auth_token = authorization_token or fastgpt_cfg['authorization_token']
    ds_id = dataset_id or fastgpt_cfg['default_dataset_id']
    
    # 请求头
    headers = {
        'Authorization': f'Bearer {auth_token}',
        'Content-Type': 'application/json'
    }
    
    # 请求体数据 - 根据auto_chunk参数决定分块模式
    data = {
        "text": text_content,
        "datasetId": ds_id,
        "parentId": None,
        "name": collection_name,
        "trainingType": training_type,
        "qaPrompt": "",
        "metadata": {}
    }
    
    if auto_chunk:
        # 自动分块模式 - 系统智能分块
        data.update({
            "chunkSettingMode": "auto"
        })
    else:
        # 自定义分块模式 - 手动指定参数
        data.update({
            "chunkSettingMode": "custom",
            "chunkSize": chunk_size,       # 分块大小
            "chunkSplitMode": "size",      # 按长度分割
            "indexSize": 512,              # 索引大小
        })
    
    try:
        # 发送 POST 请求
        response = requests.post(url, headers=headers, json=data)
        return response
    
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None
def get_collection_data_list(authorization_token=None, collection_id=None, offset=0, page_size=10, search_text=""):
    """
    获取集合的数据列表
    
    Args:
        authorization_token (str): 授权令牌
        collection_id (str): 集合ID
        offset (int): 偏移量
        page_size (int): 页面大小
        search_text (str): 搜索文本
    """
    # 获取 FastGPT 配置
    fastgpt_cfg = get_fastgpt_config()
    
    # API 端点
    url = build_api_url('get_data_list')
    
    # 使用提供的令牌或配置文件中的默认值
    auth_token = authorization_token or fastgpt_cfg['authorization_token']
    
    # 请求头
    headers = {
        'Authorization': f'Bearer {auth_token}',
        'Content-Type': 'application/json'
    }
    
    # 请求数据
    data = {
        "collectionId": collection_id,
        "offset": offset,
        "pageSize": page_size,
        "searchText": search_text
    }
    
    try:
        # 发送 POST 请求
        response = requests.post(url, headers=headers, json=data)
        return response
    
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def chunk_text_manually(text, chunk_size=1500, overlap=200):
    """
    手动分块文本的辅助函数
    
    Args:
        text (str): 要分块的文本
        chunk_size (int): 每块的大小
        overlap (int): 重叠字符数
    
    Returns:
        list: 分块后的文本列表
    """
    if not text:
        return []
    
    chunks = []
    start = 0
    text_length = len(text)
    
    while start < text_length:
        # 计算结束位置
        end = start + chunk_size
        
        # 如果不是最后一块，尝试在合适的位置断句
        if end < text_length:
            # 寻找最近的句号、换行符或空格
            for i in range(end, max(start + chunk_size // 2, start), -1):
                if text[i] in ['。', '！', '？', '\n', '；']:
                    end = i + 1
                    break
                elif text[i] == ' ':
                    end = i
                    break
        
        # 提取块
        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)
        
        # 移动到下一个起始位置（考虑重叠）
        start = max(end - overlap, start + 1)
    
    return chunks


def chunk_text_with_title(text, title, chunk_size=500, overlap=50):
    """
    将文本分块，每个分块都包含标题
    
    Args:
        text (str): 要分块的文本内容
        title (str): 报告标题，会添加到每个分块的开头
        chunk_size (int): 每块的大小
        overlap (int): 重叠字符数
    
    Returns:
        list: 分块后的文本列表，每个分块都包含标题
    """
    if not text:
        return []
    
    # 标题格式化
    formatted_title = f"报告标题：{title}\n\n"
    title_length = len(formatted_title)
    
    # 计算实际可用的内容长度（减去标题长度）
    available_chunk_size = chunk_size - title_length
    
    # 如果可用长度太小，直接返回带标题的完整文本
    if available_chunk_size <= 100:
        return [formatted_title + text]
    
    chunks = []
    start = 0
    text_length = len(text)
    
    while start < text_length:
        # 计算内容的结束位置
        end = start + available_chunk_size
        
        # 如果不是最后一块，尝试在合适的位置断句
        if end < text_length:
            # 寻找最近的句号、换行符或空格
            for i in range(end, max(start + available_chunk_size // 2, start), -1):
                if text[i] in ['。', '！', '？', '\n', '；']:
                    end = i + 1
                    break
                elif text[i] == ' ':
                    end = i
                    break
        
        # 提取内容块
        content_chunk = text[start:end].strip()
        if content_chunk:
            # 每个分块都添加标题
            full_chunk = formatted_title + content_chunk
            chunks.append(full_chunk)
        
        # 移动到下一个起始位置（考虑重叠）
        start = max(end - overlap, start + 1)
    
    return chunks


def create_collection_with_chunks(text_content, collection_name, title=None, chunk_size=500, overlap=50, 
                                 authorization_token=None, dataset_id=None, training_type="chunk"):
    """
    创建集合并智能处理分块（带标题支持）
    
    Args:
        text_content (str): 文本内容
        collection_name (str): 集合名称
        title (str, optional): 标题，如果提供则每个分块都包含此标题
        chunk_size (int): 分块大小
        overlap (int): 重叠字符数
        authorization_token (str, optional): 授权令牌
        dataset_id (str, optional): 数据集ID
        training_type (str): 训练类型
    
    Returns:
        response: API响应对象
    """
    if not text_content:
        return None
    
    # 根据是否有标题选择分块方式
    if title:
        chunks = chunk_text_with_title(text_content, title, chunk_size, overlap)
    else:
        chunks = chunk_text_manually(text_content, chunk_size, overlap)
    
    # 如果只有一个分块，直接创建集合
    if len(chunks) == 1:
        return create_text_collection_with_chunks(
            authorization_token=authorization_token,
            dataset_id=dataset_id,
            text_content=chunks[0],
            collection_name=collection_name,
            chunk_size=chunk_size,
            training_type=training_type,
            auto_chunk=False
        )
    
    # 多个分块时，先创建空集合，然后批量插入
    response = create_text_collection_with_chunks(
        authorization_token=authorization_token,
        dataset_id=dataset_id,
        text_content="",  # 空内容，稍后批量添加
        collection_name=collection_name,
        chunk_size=chunk_size,
        training_type=training_type,
        auto_chunk=False
    )
    
    # 如果集合创建成功，批量添加分块数据
    if response and response.status_code == 200:
        try:
            result = response.json()
            collection_id = result.get('data', {}).get('collectionId')
            
            if collection_id:
                # 准备批量数据
                data_list = []
                for i, chunk in enumerate(chunks):
                    data_list.append({
                        "q": chunk,
                        "a": f"{collection_name}的第{i+1}部分内容"
                    })
                
                # 批量插入
                push_response = push_data_to_collection(
                    authorization_token=authorization_token,
                    collection_id=collection_id,
                    data_list=data_list,
                    training_type=training_type
                )
                
                # 返回批量插入的结果，但保持原有response的基本信息
                if push_response and push_response.status_code == 200:
                    # 添加分块信息到原响应
                    result['data']['chunk_count'] = len(chunks)
                    return response
                else:
                    return push_response
            
        except Exception as e:
            print(f"批量插入分块数据时发生异常: {e}")
    
    return response


if __name__ == "__main__":
    print("=" * 60)
    print("FastGPT API 知识库完整测试")
    print("=" * 60)
    
    # 从配置文件获取参数
    fastgpt_cfg = get_fastgpt_config()
    your_token = fastgpt_cfg['authorization_token']
    your_dataset_id = fastgpt_cfg['default_dataset_id']
    
    print(f"🔑 使用令牌: {your_token[:20]}...")
    print(f"📊 使用数据集ID: {your_dataset_id}")
    print(f"🌐 API 基础地址: {fastgpt_cfg['base_url']}")
    
    # 测试1: 获取数据集集合列表
    print("\n=== 测试1: 获取数据集集合列表 ===")
    response = list_dataset_collection_with_config(
        authorization_token=your_token,
        dataset_id=your_dataset_id
    )
    
    collection_id = None  # 用于后续测试
    
    if response and response.status_code == 200:
        print("✅ 获取列表成功!")
        try:
            result = response.json()
            collections = result.get('data', {}).get('list', [])
            if collections:
                collection_id = collections[0]['_id']  # 获取第一个集合的ID用于后续测试
                print(f"📋 找到 {len(collections)} 个集合")
                for i, col in enumerate(collections[:3]):  # 只显示前3个
                    print(f"  {i+1}. {col['name']} (ID: {col['_id']})")
            else:
                print("📋 暂无集合数据")
        except Exception as e:
            print(f"解析响应失败: {e}")
    else:
        print(f"❌ 获取列表失败: {response.status_code if response else 'No response'}")
    
    # 测试2: 创建带自动分块的文本集合
    print("\n=== 测试2: 创建带自动分块的文本集合 ===")
    long_text = """
    人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它试图理解和构建智能实体。
    AI的目标是创建能够感知环境、学习、推理和做出决策的系统。
    
    机器学习是AI的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。
    深度学习是机器学习的一个子集，使用多层神经网络来模拟人脑的工作方式。
    
    自然语言处理（NLP）是AI的另一个重要分支，它使计算机能够理解、解释和生成人类语言。
    计算机视觉则让机器能够识别和理解数字图像和视频中的内容。
    
    AI在各个领域都有广泛应用，包括医疗诊断、自动驾驶、金融分析、推荐系统等。
    随着技术的发展，AI正在改变我们的生活方式和工作方式。
    
    """
    
    # 测试自动分块
    print("🤖 使用自动分块模式:")
    response2 = create_text_collection_with_chunks(
        authorization_token=your_token,
        dataset_id=your_dataset_id,
        text_content=long_text.strip(),
        collection_name="AI知识自动分块集合",
        training_type="chunk",
        auto_chunk=True  # 启用自动分块
    )
    
    auto_collection_id = None
    
    if response2 and response2.status_code == 200:
        print("✅ 自动分块集合创建成功!")
        try:
            result2 = response2.json()
            auto_collection_id = result2.get('data', {}).get('collectionId')
            insert_len = result2.get('data', {}).get('results', {}).get('insertLen', 0)
            print(f"📊 自动分块集合ID: {auto_collection_id}")
            print(f"📊 自动分块数量: {insert_len}")
        except Exception as e:
            print(f"解析响应失败: {e}")
    else:
        print(f"❌ 自动分块集合创建失败: {response2.status_code if response2 else 'No response'}")
        if response2:
            print(f"错误信息: {response2.text}")
    
    # 测试自定义分块
    print("\n⚙️ 使用自定义分块模式:")
    response2_custom = create_text_collection_with_chunks(
        authorization_token=your_token,
        dataset_id=your_dataset_id,
        text_content=long_text.strip(),
        collection_name="AI知识自定义分块集合",
        chunk_size=500,  # 自定义分块大小
        training_type="chunk",
        auto_chunk=False  # 使用自定义分块
    )
    
    custom_collection_id = None
    
    if response2_custom and response2_custom.status_code == 200:
        print("✅ 自定义分块集合创建成功!")
        try:
            result2_custom = response2_custom.json()
            custom_collection_id = result2_custom.get('data', {}).get('collectionId')
            insert_len_custom = result2_custom.get('data', {}).get('results', {}).get('insertLen', 0)
            print(f"📊 自定义分块集合ID: {custom_collection_id}")
            print(f"📊 自定义分块数量: {insert_len_custom}")
        except Exception as e:
            print(f"解析响应失败: {e}")
    else:
        print(f"❌ 自定义分块集合创建失败: {response2_custom.status_code if response2_custom else 'No response'}")
        if response2_custom:
            print(f"错误信息: {response2_custom.text}")
    
    # 选择一个成功创建的集合用于后续测试
    new_collection_id = auto_collection_id or custom_collection_id
    
    # 测试3: 手动分块并批量插入数据
    # print("\n=== 测试3: 手动分块文本并批量插入 ===")
    # if new_collection_id:
    #     # 准备另一段文本进行手动分块
    #     another_text = """
    #     区块链是一种分布式账本技术，它通过密码学方法将数据组织成链式结构。
    #     每个区块包含前一个区块的哈希值，形成了一个不可篡改的链条。
    #     比特币是第一个成功应用区块链技术的加密货币。
    #     以太坊扩展了区块链的概念，引入了智能合约功能。
    #     智能合约是自动执行的合约，其条款直接写入代码中。
    #     去中心化金融（DeFi）是建立在区块链上的金融服务生态系统。
    #     """
        
    #     # 手动分块
    #     chunks = chunk_text_manually(another_text.strip(), chunk_size=150, overlap=30)
    #     print(f"📝 文本被分为 {len(chunks)} 个块:")
        
    #     # 准备批量插入的数据
    #     data_list = []
    #     for i, chunk in enumerate(chunks):
    #         data_list.append({
    #             "q": chunk,
    #             "a": f"这是第{i+1}个关于区块链的知识块"
    #         })
        
    #     # 批量插入数据
    #     response3 = push_data_to_collection(
    #         authorization_token=your_token,
    #         collection_id=new_collection_id,
    #         data_list=data_list,
    #         training_type="chunk"
    #     )
        
    #     if response3 and response3.status_code == 200:
    #         print("✅ 批量插入数据成功!")
    #         try:
    #             result3 = response3.json()
    #             print(f"📊 插入结果: {json.dumps(result3, indent=2, ensure_ascii=False)}")
    #         except Exception as e:
    #             print(f"解析响应失败: {e}")
    #     else:
    #         print(f"❌ 批量插入失败: {response3.status_code if response3 else 'No response'}")
    #         if response3:
    #             print(f"错误信息: {response3.text}")
    
    # 测试4: 查看集合中的数据
    # print("\n=== 测试4: 查看集合中的数据 ===")
    # if new_collection_id:
    #     response4 = get_collection_data_list(
    #         authorization_token=your_token,
    #         collection_id=new_collection_id,
    #         page_size=20
    #     )
        
    #     if response4 and response4.status_code == 200:
    #         print("✅ 获取数据列表成功!")
    #         try:
    #             result4 = response4.json()
    #             data_list = result4.get('data', {}).get('list', [])
    #             total = result4.get('data', {}).get('total', 0)
    #             print(f"📊 总数据量: {total}")
    #             print("📋 数据预览:")
    #             for i, item in enumerate(data_list[:5]):  # 只显示前5条
    #                 print(f"  {i+1}. Q: {item.get('q', '')[:50]}...")
    #                 if item.get('a'):
    #                     print(f"     A: {item.get('a', '')[:50]}...")
    #         except Exception as e:
    #             print(f"解析响应失败: {e}")
    #     else:
    #         print(f"❌ 获取数据失败: {response4.status_code if response4 else 'No response'}")
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    print("=" * 60)