#!/usr/bin/env python3
"""
批量生成所有设备的日报任务

该模块可以用于生成指定日期所有设备的日报，作为定时任务每天晚上22点执行
"""
import sys
import os
import argparse
from datetime import datetime, timedelta

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from server.utils.daily_report_db import save_daily_report
from server.utils.get_cameras_info import get_cameras_config

def generate_all_reports(target_date=None, days_back=0):
    """
    为所有设备生成指定日期的日报
    
    Args:
        target_date (str, optional): 目标日期，格式为'YYYY-MM-DD'，默认为当前日期
        days_back (int, optional): 往前推算的天数，生成过去几天的报告，默认为0
        
    Returns:
        list: 成功生成日报的设备ID列表
    """
    # 确定目标日期
    if target_date is None:
        # 如果没有指定目标日期，使用当前日期
        base_date = datetime.now()
    else:
        # 如果指定了目标日期，解析该日期
        base_date = datetime.strptime(target_date, '%Y-%m-%d')
    
    # 计算实际的报告日期
    report_date = (base_date - timedelta(days=days_back)).strftime('%Y-%m-%d')
    
    # 获取所有摄像头配置
    print(f"获取摄像头配置信息...")
    cameras_config = get_cameras_config()
    camera_ids = [camera.get('camera_id') for camera in cameras_config if camera.get('camera_id')]
    
    print(f"共找到 {len(camera_ids)} 个设备，将为 {report_date} 生成日报")
    
    # 存储成功生成日报的设备ID
    successful_devices = []
    failed_devices = []
    
    # 为每个设备生成日报
    for i, camera_id in enumerate(camera_ids):
        print(f"[{i+1}/{len(camera_ids)}] 正在为设备 {camera_id} 生成 {report_date} 的日报...")
        try:
            report_id = save_daily_report(camera_id, report_date)
            if report_id:
                print(f"设备 {camera_id} 的日报生成成功，ID: {report_id}")
                successful_devices.append(camera_id)
            else:
                print(f"设备 {camera_id} 的日报生成失败")
                failed_devices.append(camera_id)
        except Exception as e:
            print(f"设备 {camera_id} 的日报生成过程中出错: {e}")
            failed_devices.append(camera_id)
    
    # 打印统计信息
    print("\n===== 生成结果统计 =====")
    print(f"日期: {report_date}")
    print(f"总设备数: {len(camera_ids)}")
    print(f"成功生成: {len(successful_devices)}")
    print(f"生成失败: {len(failed_devices)}")
    
    if failed_devices:
        print("\n以下设备的日报生成失败:")
        for device_id in failed_devices:
            print(f"  - {device_id}")
    
    return successful_devices

def start_daily_reports_task():
    """
    定时任务入口函数，生成当天的所有设备日报
    
    在app_run.py中作为定时任务每天晚上22点执行
    """
    print(f"开始执行设备日报生成任务，时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 默认生成当天的日报
    successful_devices = generate_all_reports()
    
    print(f"设备日报生成任务完成，成功生成 {len(successful_devices)} 个设备的日报")
    return successful_devices

# 如果直接运行此脚本，则执行日报生成任务
if __name__ == "__main__":
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="批量生成所有设备的日报")
    parser.add_argument("--date", "-d", help="目标日期，格式为YYYY-MM-DD，默认为当前日期")
    parser.add_argument("--days-back", "-b", type=int, default=0, help="往前推算的天数，生成过去几天的报告，默认为0")
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 调用函数生成日报
    generate_all_reports(args.date, args.days_back) 