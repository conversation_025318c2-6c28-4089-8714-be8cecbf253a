"""
运行的主程序
"""
import logging
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from pathlib import Path
from typing import Dict, List
from config_file import config
import cv2
import requests
import yaml
import os
try:
    import av
    PYAV_AVAILABLE = True
except ImportError:
    PYAV_AVAILABLE = False
    logging.warning("PyAV库未安装，将使用OpenCV进行视频帧获取")

from server.remote.device import get_device_list, process_device_data
from server.remote.point import APIClient
from server.utils.cleanup import DataCleanupManager
from server.utils.data_publisher import DataPublisher
from server.utils.env_manager import EnvManager
from server.utils.logger import setup_logging
from server.utils.path_manager import PathManager
from server.utils.sensor_manager import SensorManager
from server.utils.video_processor import VideoFrameProcessor
from server.utils.mqtt_tool import publish_foam_data
from server.utils.get_cameras_info import get_cameras_config,get_robot_status_info
from datetime import datetime
from llms.yolo_region import YoloRegion<PERSON>ounter
# 配置日志

class VideoMonitoringSystem:
    def __init__(self):
        """初始化视频监控系统"""
        # 初始化路径管理器
        self.path_manager = PathManager()

        self.config = config.env

        # 使用路径管理器获取其他路径
        self.base_dataset_path = self.path_manager.get_path('dataset')

        # 获取API URL
        self.env_api_url = self.path_manager.get_api_url('env_url')

        # 从配置文件加载系统参数
        api_config = self.config['system']['api']
        retry_config = self.config['system']['retry']
        thread_config = self.config['system']['thread_pool']
        storage_config = self.config['storage']
        cleanup_config = self.config['cleanup']

        # 设置类常量
        self.MAX_RETRIES = retry_config['max_retries']
        self.RETRY_DELAY = retry_config['delay']
        self.MAX_WORKERS = thread_config['max_workers']
        self.API_TIMEOUT = api_config['timeout']
        self.IMAGE_RETENTION_DAYS = cleanup_config['image_retention_days']
        self.CLEANUP_MONTHS = cleanup_config['cleanup_months']
        self.CLEANUP_DAY = cleanup_config['cleanup_day']

        # 验证并设置API URL
        self.check_interval = float(api_config['check_interval'])

        # 初始化其他属性
        self.is_api_running = False
        self.camera_threads: Dict[str, threading.Thread] = {}
        self.camera_captures: Dict[str, cv2.VideoCapture] = {}
        self.camera_executors: Dict[str, ThreadPoolExecutor] = {}
        self.stop_flags: Dict[str, bool] = {}

        # 设置路径
        self.base_dataset_path = Path(storage_config['paths']['base_dataset'])
        self.system_running = True

        # 初始化API客户端
        self.api_client = APIClient()
        self.base_url = api_config['base_env_url']

        # 初始化清理管理器
        self.cleanup_manager = DataCleanupManager(self.config)

        # 初始化各个管理器
        self.video_processor = VideoFrameProcessor(self.config)
        self.sensor_manager = SensorManager(self.config)
        self.data_publisher = DataPublisher(self.config)
        # 定义检测区域
        self.region_points = {
            "region-01": [(60, 550), (600, 550), (600, 300), (60, 300)],
            "region-02": [(600, 550), (1200, 550), (1200, 300), (600, 300)],
            "region-03": [(1200, 550), (1800, 550), (1800, 300), (1200, 300)],
        }
        self.yolo_model = self.config['yolo_model']
        
        # 初始化测试模式
        self.test_mode = False
        # 测试图像路径列表
        self.test_images = [
            # ---------------------------------- 判断倾斜的逻辑 --------------------------------- #
            # '/home/<USER>/llm_project/yolo_project/datasets/竹料/test/1.jpg',
            # '/home/<USER>/llm_project/yolo_project/datasets/竹料/test/1.jpg',
            '/home/<USER>/llm_project/yolo_project/datasets/竹料/test/10.jpg',
            '/home/<USER>/llm_project/yolo_project/datasets/竹料/test/6.jpg',
            '/home/<USER>/llm_project/yolo_project/datasets/竹料/test/19.jpg',
            '/home/<USER>/llm_project/yolo_project/datasets/竹料/test/28.jpg',

            # ---------------------------------- 判断卡住的逻辑 --------------------------------- #
            # '/home/<USER>/llm_project/yolo_project/datasets/竹料/test/卡住/1.jpg',
            # '/home/<USER>/llm_project/yolo_project/datasets/竹料/test/卡住/2.jpg',
            # '/home/<USER>/llm_project/yolo_project/datasets/竹料/test/卡住/2.jpg',
            # ======================= 测试整体视角分析 ======================= #
            # 'assets/secondary_clarifier_test.jpg',
            # ========================= 树叶识别(机器人行进的时候不定时识别)========================= #
            # 'assets/leaf_recognition_test.jpg',
            # ========================= 排浮渣识别(机器人停止的时候识别) ========================= #
            # 'assets/slag_outletv2_test.png',
            # ========================= 水量对比(机器人行进的时候定时识别,目前是45分钟) ========================= #
            # 'assets/robot1.png',
            # 'assets/robot2.png',

        ]
        # 支持从文件夹加载测试图像的方法
        self.load_test_images_from_folder = self.load_images_from_folder
        self.current_test_image_index = 0
        
        
    def start_test_mode(self, test_path=None):
        """开始测试模式，使用test_images进行耙斗倾斜和卡住的测试
        
        Args:
            test_path (str, optional): 测试图像路径或文件夹路径。如果提供文件夹路径，将加载文件夹中所有图像。
        
        Returns:
            bool: 是否成功启动测试模式
        """
        if test_path:
            # 检查是否是文件夹路径
            path = Path(test_path)
            if path.is_dir():
                # 如果是文件夹，加载文件夹中的所有图像
                if not self.set_test_images_folder(test_path):
                    return False
            elif path.is_file():
                # 如果是单个文件，只使用这个文件
                self.test_images = [str(path)]
            else:
                logging.error(f"无效的测试路径: {test_path}")
                return False
        
        if not self.test_images:
            logging.error("没有配置测试图片，无法启动测试模式")
            return False
            
        self.test_mode = True
        self.current_test_image_index = 0
        logging.info(f"已启动测试模式，将使用 {len(self.test_images)} 张测试图片")
        
        # 将测试图片传递给视频处理器
        self.video_processor.test_images = self.test_images
        self.video_processor.start_test_mode()
        return True
        
    def stop_test_mode(self):
        """停止测试模式
        
        Returns:
            bool: 是否成功停止测试模式
        """
        if self.test_mode:
            self.test_mode = False
            logging.info("已停止测试模式")
            self.video_processor.stop_test_mode()
            return True
        return False
        
    def load_images_from_folder(self, folder_path):
        """从文件夹中加载所有图像文件
        
        Args:
            folder_path (str): 图像文件夹路径
            
        Returns:
            list: 图像文件路径列表
        """
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff']
        image_files = []
        
        try:
            folder = Path(folder_path)
            if not folder.exists() or not folder.is_dir():
                logging.error(f"文件夹路径无效: {folder_path}")
                return image_files
                
            # 遍历文件夹中的所有文件
            for file_path in folder.glob('**/*'):
                if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                    image_files.append(str(file_path))
                    
            # 排序确保每次读取顺序一致
            image_files.sort()
            
            logging.info(f"从文件夹 {folder_path} 中加载了 {len(image_files)} 张图像")
        except Exception as e:
            logging.error(f"加载图像文件时发生错误: {str(e)}")
            
        return image_files
        
    def set_test_images_folder(self, folder_path):
        """设置测试图像文件夹，并从中加载所有图像
        
        Args:
            folder_path (str): 图像文件夹路径
            
        Returns:
            bool: 是否成功设置测试图像
        """
        image_files = self.load_images_from_folder(folder_path)
        if not image_files:
            logging.error(f"文件夹 {folder_path} 中未找到有效图像文件")
            return False
            
        self.test_images = image_files
        self.current_test_image_index = 0
        logging.info(f"已设置测试图像文件夹: {folder_path}，共 {len(self.test_images)} 张图像")
        
        # 将测试图片传递给视频处理器
        if hasattr(self.video_processor, 'test_images'):
            self.video_processor.test_images = self.test_images
            
        return True
        
    def process_frame(self, frame, frame_count, save_dir, camera_id, video_id,
                      threshold, system_type, standard_image_path, current_time,video_path_rtsp):
        """
        处理单个视频帧

        Args:
            frame: 视频帧数据(numpy数组)
            frame_count: 帧计数器
            save_dir: 保存目录路径
            camera_id: 摄像头ID
            video_id: 视频ID
            threshold: 覆盖率阈值
            system_type: 系统类型
            standard_image_path: 标准图像路径(可选)
            current_time: 当前时间
            video_path_rtsp: 视频路径
        """
        try:
            # 记录开始处理帧的时间
            start_time = time.time()
            
            # 确保输入参数的类型正确
            if frame is None:
                logging.error(f"摄像头 {camera_id} 的帧数据为None，无法处理")
                return
                
            if threshold is None:
                logging.warning(f"摄像头 {camera_id} 的阈值为None，使用默认值0.5")
                threshold = 60
                
            if system_type is None:
                logging.warning(f"摄像头 {camera_id} 的系统类型为None，使用默认值'default'")
                system_type = 'default'
                
            if video_id is None:
                logging.warning(f"摄像头 {camera_id} 的视频ID为None，使用摄像头ID作为默认值")
                video_id = str(camera_id)
                
            # 确保frame_count是整数
            try:
                frame_count = int(frame_count)
            except (TypeError, ValueError):
                logging.warning(f"摄像头 {camera_id} 的帧计数器 {frame_count} 不是有效整数，使用0作为默认值")
                frame_count = 0
                
            # 记录帧处理参数 - 将多条debug日志合并为一条
            logging.debug(f"摄像头 {camera_id} 开始处理帧 {frame_count} - 系统类型: {system_type}, 阈值: {threshold}")
            
            if "filter" in system_type.lower() or "bucket" in system_type.lower():
                sensor_data = None
                sensor_data_do = 0
                sensor_data_mlss = 0
                # coverage_float_publish = 0
            elif "moss" in system_type.lower() or "slag_outlet" in system_type.lower():
                sensor_data = None
                sensor_data_do = 0
                sensor_data_mlss = 0
            else:
                # 获取传感器数据
                sensor_data = self.sensor_manager.get_sensor_data(camera_id)
                if not sensor_data:
                    logging.debug(f"摄像头 {camera_id} 帧 {frame_count}: 未获取到传感器数据")
                    return
                sensor_data_do = sensor_data.get('DO', -100)
                sensor_data_mlss = sensor_data.get('MLSS', -100)
                # 将这行日志注释掉或修改为更低频率的记录
                # logging.debug(f"摄像头 {camera_id} 帧 {frame_count}: 获取传感器数据 DO={sensor_data_do}, MLSS={sensor_data_mlss}")

            # ***************** 大模型/耙斗倾斜的检测 **************** #
            # 处理视频帧 - 注释掉这行日志，减少输出
            # logging.debug(f"摄像头 {camera_id} 帧 {frame_count}: 开始调用视频处理器")
            (coverage_float, analysis_result, alarm_status_flag, is_abnormal,
             frame_path, suggestion,failure_reasons_type) = self.video_processor.process_frame_filter(
                frame, frame_count, save_dir, camera_id, sensor_data,
                threshold, system_type, standard_image_path, current_time
            )
            # 将这行日志注释掉，在最后统一输出处理结果
            # logging.debug(f"摄像头 {camera_id} 帧 {frame_count}: 视频处理完成 - 覆盖率: {coverage_float}, 报警状态: {alarm_status_flag}, 异常: {is_abnormal}")

            # 准备并发布数据 determine_coverage_level()根据泡沫面积判断等级
            if system_type in ['system_prompt_aerobic_single1', 'system_prompt_aerobic_multiple1', 'system_prompt_aerobic_single2', 'system_prompt_aerobic_multiple2']: # 好氧池的根据其他状态判断报警信息
                coverage_level, alarm_status = self.video_processor.alarm_status(alarm_status_flag)
                
            else: # 其他的暂时还是按照原来根据泡沫面积计算覆盖水平和报警
                coverage_level = self.video_processor.determine_coverage_level(coverage_float)
                alarm_status = alarm_status_flag
            # 增加一个报警类型
            # alarmtype = ''
            if len(failure_reasons_type) > 0:
                alarmtype = '运行故障,#f05454'
            else:
                alarmtype = ''
            frame_data = (
                camera_id, video_id, int(frame_count), current_time,
                frame_path, coverage_float,
                coverage_level,
                alarm_status, analysis_result, is_abnormal,
                sensor_data_do, sensor_data_mlss, suggestion,failure_reasons_type,alarmtype
            )

            self.data_publisher.publish_data(frame_data)
            
            # 记录帧处理完成时间和耗时
            end_time = time.time()
            processing_time = end_time - start_time
            
            # 合并多条debug日志为一条，包含所有关键信息
            if frame_count % 5 == 0:  # 每5帧记录一次详细日志，减少日志量
                logging.debug(f"摄像头 {camera_id} 帧 {frame_count}: 处理完成 - 耗时: {processing_time:.2f}秒, 覆盖率: {coverage_float}, 报警: {alarm_status}, 异常: {is_abnormal}, 传感器: DO={sensor_data_do}, MLSS={sensor_data_mlss}")

        except Exception as e:
            logging.exception(f"处理帧 {frame_count} 时发生错误: {str(e)}")


    def process_single_camera(self, camera_config: dict):
        """
        处理单个摄像头的视频流
        :param camera_config: 摄像头配置信息
        """
        camera_id = camera_config['camera_id']
        video_paths = camera_config['video_path'] if isinstance(camera_config['video_path'], list) else [
            camera_config['video_path']]

        # 确保关键参数不为None
        if 'threshold' not in camera_config or camera_config['threshold'] is None:
            logging.warning(f"摄像头 {camera_id} 的阈值为None，使用默认值0.5")
            camera_config['threshold'] = 60
            
        if 'system_type' not in camera_config or camera_config['system_type'] is None:
            logging.warning(f"摄像头 {camera_id} 的系统类型为None")
            print(f"摄像头 {camera_id} 的系统类型为None.")
            
        if 'video_id' not in camera_config or camera_config['video_id'] is None:
            logging.warning(f"摄像头 {camera_id} 的视频ID为None，使用摄像头ID作为默认值")
            camera_config['video_id'] = str(camera_id)
            
        # 获取视频帧获取方式
        video_capture_method = self.config['system'].get('video_capture', {}).get('method', 'opencv')
        
        try:
            if video_capture_method == 'pyav' and PYAV_AVAILABLE:
                self._process_with_pyav(camera_config, camera_id, video_paths)
            else:
                self._process_with_opencv(camera_config, camera_id, video_paths)
        except Exception as e:
            logging.error(f"摄像头 {camera_id} 线程发生错误: {str(e)}")
            
    def _process_with_opencv(self, camera_config: dict, camera_id: str, video_paths: list):
        """使用OpenCV处理视频流"""
        try:
            with ThreadPoolExecutor(max_workers=4) as executor:
                self.camera_executors[camera_id] = executor
                config_msg = (f"摄像头配置信息 - ID: {camera_id}, "
                              f"视频路径: {video_paths}, "
                              f"阈值: {camera_config['threshold']}, "
                              f"帧间隔: {camera_config['frame_interval_all']}秒, "
                              f"处理方式: OpenCV")
                print(f"{time.strftime('%Y-%m-%d %H:%M:%S')} {config_msg}")
                logging.info(config_msg)

                retry_count = 0
                max_retries = self.MAX_RETRIES  # 使用系统配置的最大重试次数
                frame_success_count = 0  # 成功读取帧的计数
                min_frames_for_reset = 10  # 至少成功读取这么多帧才重置重试计数
                
                while not self.stop_flags[camera_id]:
                    try:
                        if not self.check_api_status():
                            logging.warning(f"摄像头 {camera_id}: API服务器已停止，终止视频处理")
                            break
                            
                        # 只有在成功读取了足够多的帧后才重置重试计数器
                        if frame_success_count >= min_frames_for_reset:
                            logging.info(f"摄像头 {camera_id}: 已成功读取 {frame_success_count} 帧，重置重试计数")
                            retry_count = 0
                            frame_success_count = 0

                        # ************************ 处理测试模式 ************************ #
                        if self.test_mode and (camera_config.get('system_type') == 'system_prompt_bucket_dipper' or
                                            'system_prompt_holistic_perspective' in camera_config.get('system_type', '') or
                                            'system_prompt_leaf_recognition' in camera_config.get('system_type', '') or
                                            'system_prompt_slag_outletv2' in camera_config.get('system_type', '')):
                            # 测试模式下不使用摄像头，直接从测试图像列表中获取图像
                            current_time = datetime.now()
                            save_dir = Path(f"{self.base_dataset_path}/{current_time.strftime('%Y/%m/%d')}")
                            save_dir.mkdir(parents=True, exist_ok=True)
                            
                            # 获取测试图片
                            if not self.test_images:
                                logging.error("测试图片列表为空")
                                time.sleep(5)
                                continue
                                
                            test_image_path = self.test_images[self.current_test_image_index]
                            print(f"---------------------------")
                            print(f"处理测试图片: {test_image_path} ({self.current_test_image_index + 1}/{len(self.test_images)})")
                            print(f"---------------------------")
                            
                            # 读取测试图片
                            frame = cv2.imread(test_image_path)
                            if frame is None:
                                logging.error(f"无法读取测试图片: {test_image_path}")
                                # 跳过无法读取的图片
                                self.current_test_image_index = (self.current_test_image_index + 1) % len(self.test_images)
                                time.sleep(1)
                                continue
                                
                            # 更新测试图片索引
                            self.current_test_image_index = (self.current_test_image_index + 1) % len(self.test_images)
                            
                            # 确保测试模式下的参数不为None
                            threshold = camera_config.get('threshold', 60)
                            system_type = camera_config.get('system_type', '')
                            video_id = camera_config.get('video_id', str(camera_id))
                            
                            # 处理测试图片
                            standard_image_path = video_paths[0] if len(video_paths) > 1 else None
                            executor.submit(
                                self.process_frame,
                                frame.copy(),
                                self.current_test_image_index,
                                save_dir,
                                camera_config['camera_id'],
                                video_id,
                                threshold,
                                system_type,
                                standard_image_path,
                                current_time,
                                camera_config['video_path']
                            )
                            
                            # 使用配置文件中的帧间隔时间
                            test_frame_interval = self.config.get('system', {}).get('test_mode_config', {}).get('frame_interval', 5)
                            time.sleep(test_frame_interval)
                            continue
                            
                        # 常规模式下的处理
                        cap = self.camera_captures.get(camera_id)
                        if cap is None or not cap.isOpened():
                            if cap is not None:
                                cap.release()  # 确保旧的 capture 被释放
                                logging.info(f"摄像头 {camera_id}: 释放旧的视频捕获对象")
                            
                            connect_msg = f"正在连接摄像头 {camera_id} - 视频路径: {video_paths[-1]}"
                            print(f"{time.strftime('%Y-%m-%d %H:%M:%S')} {connect_msg}")
                            logging.info(connect_msg)

                            cap = cv2.VideoCapture(video_paths[-1])
                            if not cap.isOpened():
                                retry_count += 1
                                error_msg = f"摄像头 {camera_id}: 无法打开视频流 - 路径: {video_paths[-1]}, 重试次数: {retry_count}/{max_retries}"
                                print(f"{time.strftime('%Y-%m-%d %H:%M:%S')} {error_msg}")
                                logging.error(error_msg)
                                
                                # 检查是否达到最大重试次数
                                if retry_count >= max_retries:
                                    logging.warning(f"摄像头 {camera_id}: 达到最大重试次数 {max_retries}，暂停重试10分钟")
                                    time.sleep(600)  # 暂停10分钟
                                    retry_count = 0  # 重置重试计数
                                else:
                                    # 使用指数退避策略，每次失败后等待时间增加
                                    wait_time = min(5 * (2 ** (retry_count - 1)), 60)  # 最长等待60秒
                                    logging.warning(f"摄像头 {camera_id}: 连接失败，等待 {wait_time} 秒后重试 (状态: 连接失败)")
                                    time.sleep(wait_time)
                                continue
                            
                            self.camera_captures[camera_id] = cap
                            logging.info(f"摄像头 {camera_id}: 成功连接到视频流 - 路径: {video_paths[-1]}")

                        fps = cap.get(cv2.CAP_PROP_FPS)
                        if fps <= 0:  # 检查帧率是否有效
                            logging.warning(f"摄像头 {camera_id} 返回无效帧率，使用默认值25")
                            fps = 25.0

                        fps_message = f"摄像头 {camera_id} ({video_paths[-1]}) 的帧率: {fps}"
                        print(f"{time.strftime('%Y-%m-%d %H:%M:%S')} {fps_message}")
                        logging.info(fps_message)

                        # 确保frame_interval_all不为None
                        frame_interval = camera_config.get('frame_interval_all')
                        if frame_interval is None:
                            logging.warning(f"摄像头 {camera_id} 的帧间隔为None，使用默认值60.0")
                            frame_interval = 60.0
                        
                        frames_to_skip = int(fps * frame_interval)
                        frame_count = 0
                        last_frame_time = time.time()  # 记录上一帧的时间

                        while not self.stop_flags[camera_id]:
                            try:
                                current_frame_time = time.time()
                                frame_time_diff = current_frame_time - last_frame_time
                                
                                ret, frame = cap.read()
                                if not ret:
                                    # 增加重试计数，这样即使是"连接成功但读帧失败"的情况也会计入重试次数
                                    retry_count += 1
                                    logging.warning(f"摄像头 {camera_id}: 视频流读取失败，尝试重新连接... 重试次数: {retry_count}/{max_retries} (状态: 读取帧失败)")
                                    # 显式释放资源
                                    if camera_id in self.camera_captures:
                                        try:
                                            self.camera_captures[camera_id].release()
                                            self.camera_captures[camera_id] = None
                                        except Exception as e:
                                            logging.error(f"释放摄像头 {camera_id} 资源时发生错误: {str(e)}")
                                    
                                    # 检查是否达到最大重试次数
                                    if retry_count >= max_retries:
                                        logging.warning(f"摄像头 {camera_id}: 达到最大重试次数 {max_retries}，暂停重试10分钟 (状态: 最大重试次数)")
                                        time.sleep(600)  # 暂停10分钟
                                        retry_count = 0  # 重置重试计数
                                    else:
                                        # 使用指数退避策略
                                        wait_time = min(5 * (2 ** (retry_count - 1)), 60)  # 最长等待60秒
                                        logging.warning(f"摄像头 {camera_id}: 等待 {wait_time} 秒后重试 (状态: 读取帧失败)")
                                        time.sleep(wait_time)
                                    break

                                current_time = datetime.now()
                                save_dir = Path(f"{self.base_dataset_path}/{current_time.strftime('%Y/%m/%d')}")
                                save_dir.mkdir(parents=True, exist_ok=True)

                                if frame_count % frames_to_skip == 0:
                                    # 记录处理帧的详细信息
                                    logging.info(f"摄像头 {camera_id}: 处理第 {frame_count} 帧，距上一帧 {frame_time_diff:.2f} 秒 (状态: 正常处理)")
                                    standard_image_path = video_paths[0] if len(video_paths) > 1 else None
                                    executor.submit(
                                        self.process_frame,
                                        frame.copy(),
                                        frame_count,
                                        save_dir,
                                        camera_config['camera_id'],
                                        camera_config['video_id'],
                                        camera_config['threshold'],
                                        camera_config['system_type'],
                                        standard_image_path,
                                        current_time,
                                        camera_config['video_path']
                                    )
                                else:
                                    # 记录跳帧的详细信息，但每30帧只记录一次，避免日志过多
                                    if frame_count % 30 == 0:
                                        logging.debug(f"摄像头 {camera_id}: 跳过第 {frame_count} 帧，距上一帧 {frame_time_diff:.2f} 秒 (状态: 跳帧)")

                                frame_count += 1
                                last_frame_time = current_frame_time  # 更新上一帧的时间
                                # 增加成功读取帧的计数
                                frame_success_count += 1
                            except Exception as e:
                                logging.exception(f"处理帧 {frame_count} 时发生错误: {str(e)} (状态: 处理异常)")
                                break

                    except Exception as e:
                        retry_count += 1
                        logging.error(f"摄像头 {camera_id} 处理时发生错误: {str(e)}, 重试次数: {retry_count}/{max_retries}")
                        if camera_id in self.camera_captures:
                            try:
                                self.camera_captures[camera_id].release()
                                self.camera_captures[camera_id] = None
                            except Exception as release_error:
                                logging.error(f"释放摄像头 {camera_id} 资源时发生错误: {str(release_error)}")
                                
                        # 检查是否达到最大重试次数
                        if retry_count >= max_retries:
                            logging.warning(f"摄像头 {camera_id}: 达到最大重试次数 {max_retries}，暂停重试10分钟")
                            time.sleep(600)  # 暂停10分钟
                            retry_count = 0  # 重置重试计数
                        else:
                            # 使用指数退避策略，每次失败后等待时间增加
                            wait_time = min(10 * (2 ** (retry_count - 1)), 120)  # 最长等待120秒
                            time.sleep(wait_time)

        except Exception as e:
            logging.error(f"摄像头 {camera_id} 线程发生错误: {str(e)}")
        finally:
            # 确保在线程结束时释放资源
            if camera_id in self.camera_captures:
                try:
                    self.camera_captures[camera_id].release()
                except:
                    pass
            if camera_id in self.camera_executors:
                try:
                    self.camera_executors[camera_id].shutdown(wait=False)
                except:
                    pass
                    
    def _process_with_pyav(self, camera_config: dict, camera_id: str, video_paths: list):
        """使用PyAV处理视频流"""
        try:
            with ThreadPoolExecutor(max_workers=4) as executor:
                self.camera_executors[camera_id] = executor
                
                config_msg = (f"摄像头配置信息 - ID: {camera_id}, "
                            f"视频路径: {video_paths}, "
                            f"阈值: {camera_config['threshold']}, "
                            f"帧间隔: {camera_config['frame_interval_all']}秒, "
                            f"处理方式: PyAV")
                print(f"{time.strftime('%Y-%m-%d %H:%M:%S')} {config_msg}")
                logging.info(config_msg)

                retry_count = 0
                max_retries = self.MAX_RETRIES  # 使用系统配置的最大重试次数
                frame_success_count = 0  # 成功读取帧的计数
                min_frames_for_reset = 10  # 至少成功读取这么多帧才重置重试计数
                
                while not self.stop_flags[camera_id]:
                    try:
                        if not self.check_api_status():
                            logging.warning(f"摄像头 {camera_id}: API服务器已停止，终止视频处理")
                            break
                            
                        # 只有在成功读取了足够多的帧后才重置重试计数器
                        if frame_success_count >= min_frames_for_reset:
                            logging.info(f"摄像头 {camera_id}: 已成功读取 {frame_success_count} 帧，重置重试计数")
                            retry_count = 0
                            frame_success_count = 0

                        # 测试模式处理与OpenCV相同
                        if self.test_mode and (camera_config.get('system_type') == 'system_prompt_bucket_dipper' or
                                            'system_prompt_holistic_perspective' in camera_config.get('system_type', '') or
                                            'system_prompt_leaf_recognition' in camera_config.get('system_type', '') or
                                            'system_prompt_slag_outletv2' in camera_config.get('system_type', '')):
                            # 使用与OpenCV相同的测试模式代码
                            current_time = datetime.now()
                            save_dir = Path(f"{self.base_dataset_path}/{current_time.strftime('%Y/%m/%d')}")
                            save_dir.mkdir(parents=True, exist_ok=True)
                            
                            # 获取测试图片
                            if not self.test_images:
                                logging.error("测试图片列表为空")
                                time.sleep(5)
                                continue
                                
                            test_image_path = self.test_images[self.current_test_image_index]
                            print(f"---------------------------")
                            print(f"处理测试图片: {test_image_path} ({self.current_test_image_index + 1}/{len(self.test_images)})")
                            print(f"---------------------------")
                            
                            frame = cv2.imread(test_image_path)
                            if frame is None:
                                logging.error(f"无法读取测试图片: {test_image_path}")
                                # 跳过无法读取的图片
                                self.current_test_image_index = (self.current_test_image_index + 1) % len(self.test_images)
                                time.sleep(1)
                                continue
                                
                            self.current_test_image_index = (self.current_test_image_index + 1) % len(self.test_images)
                            
                            threshold = camera_config.get('threshold', 60)
                            system_type = camera_config.get('system_type', '')
                            video_id = camera_config.get('video_id', str(camera_id))
                            
                            # 处理测试图片
                            standard_image_path = video_paths[0] if len(video_paths) > 1 else None
                            executor.submit(
                                self.process_frame,
                                frame.copy(),
                                self.current_test_image_index,
                                save_dir,
                                camera_config['camera_id'],
                                video_id,
                                threshold,
                                system_type,
                                standard_image_path,
                                current_time,
                                camera_config['video_path']
                            )
                            
                            # 使用配置文件中的帧间隔时间
                            test_frame_interval = self.config.get('system', {}).get('test_mode_config', {}).get('frame_interval', 5)
                            time.sleep(test_frame_interval)
                            continue

                        # 使用PyAV打开视频流
                        try:
                            # 打开RTSP流，设置网络和缓冲参数
                            options = {
                                "probesize": "5000000" ,        # 设置probesize选项为5000000字节
                                "analyzeduration": "10000000",  # 设置analyzeduration选项为10秒
                                'rtsp_transport': 'tcp',        # 使用TCP而非UDP
                                'buffer_size': '1024000',       # 增加缓冲区大小
                                'max_delay': '500000',          # 最大延迟(微秒)
                                'stimeout': '5000000',          # 套接字超时(微秒)
                                'reorder_queue_size': '5000',   # 重排序队列大小
                            }
                            logging.info(f"摄像头 {camera_id} (PyAV): 尝试连接视频流 - 路径: {video_paths[-1]}")
                            container = av.open(video_paths[-1], options=options)
                            video_id = camera_config.get('video_id', str(camera_id))
                            logging.info(f"摄像头 {camera_id} (PyAV): 成功打开视频流: {video_paths[-1]} (状态: 连接成功)")
                        except Exception as e:
                            video_id = camera_config.get('video_id', str(camera_id))
                            retry_count += 1
                            error_msg = f"摄像头 {camera_id} (视频ID: {video_id}): 无法打开视频流 - 路径: {video_paths[-1]}, 错误: {str(e)}, 重试次数: {retry_count}/{max_retries}"
                            print(f"{time.strftime('%Y-%m-%d %H:%M:%S')} {error_msg}")
                            logging.error(error_msg)
                            logging.error(f"摄像头 {camera_id} (PyAV): 连接失败详情 - {str(e)} (状态: 连接失败)")
                            
                            # 检查是否达到最大重试次数
                            if retry_count >= max_retries:
                                logging.warning(f"摄像头 {camera_id} (视频ID: {video_id}): 达到最大重试次数 {max_retries}，暂停重试10分钟 (状态: 最大重试次数)")
                                time.sleep(600)  # 暂停10分钟
                                retry_count = 0  # 重置重试计数
                            else:
                                # 使用指数退避策略，每次失败后等待时间增加
                                wait_time = min(5 * (2 ** (retry_count - 1)), 60)  # 最长等待60秒
                                logging.warning(f"摄像头 {camera_id} (PyAV): 等待 {wait_time} 秒后重试 (状态: 连接失败)")
                                time.sleep(wait_time)
                            continue

                        # 确保frame_interval_all不为None
                        frame_interval = camera_config.get('frame_interval_all')
                        if frame_interval is None:
                            logging.warning(f"摄像头 {camera_id} 的帧间隔为None，使用默认值60.0")
                            frame_interval = 60.0

                        frame_count = 0
                        last_process_time = time.time()
                        logging.info(f"摄像头 {camera_id} (PyAV): 开始处理视频流，帧间隔设置为 {frame_interval} 秒")

                        for frame in container.decode(video=0):
                            if self.stop_flags[camera_id]:
                                logging.info(f"摄像头 {camera_id} (PyAV): 检测到停止标志，终止处理")
                                break

                            current_time = time.time()
                            time_since_last = current_time - last_process_time
                            
                            # 完全注释掉这部分，避免任何日志输出
                            # if frame_count % 10 == 0:  # 每10帧记录一次，避免日志过多
                            #     logging.debug(f"摄像头 {camera_id} (PyAV): 当前帧 {frame_count}, 距上次处理 {time_since_last:.2f} 秒")
                            
                            if current_time - last_process_time >= frame_interval:
                                try:
                                    frame_np = frame.to_ndarray(format='bgr24')
                                    current_datetime = datetime.now()
                                    save_dir = Path(f"{self.base_dataset_path}/{current_datetime.strftime('%Y/%m/%d')}")
                                    save_dir.mkdir(parents=True, exist_ok=True)

                                    standard_image_path = video_paths[0] if len(video_paths) > 1 else None
                                    
                                    # 记录处理帧的详细信息 - 添加采样，每10帧记录一次
                                    if frame_count % 10 == 0:
                                        logging.info(f"摄像头 {camera_id} (PyAV): 处理第 {frame_count} 帧，距上次处理 {time_since_last:.2f} 秒 (状态: 正常处理)")
                                    
                                    executor.submit(
                                        self.process_frame,
                                        frame_np,
                                        frame_count,
                                        save_dir,
                                        camera_config['camera_id'],
                                        camera_config['video_id'],
                                        camera_config['threshold'],
                                        camera_config['system_type'],
                                        standard_image_path,
                                        current_datetime,
                                        camera_config['video_path']
                                    )

                                    frame_count += 1
                                    # 增加成功读取帧的计数
                                    frame_success_count += 1
                                    last_process_time = current_time
                                except Exception as e:
                                    logging.exception(f"摄像头 {camera_id} (PyAV): 处理帧 {frame_count} 时发生错误: {str(e)} (状态: 处理异常)")
                                    continue
                            else:
                                # 完全注释掉这部分，避免任何日志输出
                                # if frame_count % 30 == 0:  # 每30帧记录一次跳帧信息，避免日志过多
                                #     logging.debug(f"摄像头 {camera_id} (PyAV): 跳过当前帧 {frame_count}，还需等待 {frame_interval - time_since_last:.2f} 秒 (状态: 跳帧)")
                                pass

                        if 'container' in locals():
                            logging.info(f"摄像头 {camera_id} (PyAV): 关闭视频流容器")
                            container.close()

                    except Exception as e:
                        video_id = camera_config.get('video_id', str(camera_id))
                        retry_count += 1
                        logging.error(f"摄像头 {camera_id} (视频ID: {video_id}) 处理时发生错误: {str(e)}, 重试次数: {retry_count}/{max_retries}")
                        
                        # 检查是否达到最大重试次数
                        if retry_count >= max_retries:
                            logging.warning(f"摄像头 {camera_id} (视频ID: {video_id}): 达到最大重试次数 {max_retries}，暂停重试5分钟")
                            time.sleep(300)  # 暂停5分钟
                            retry_count = 0  # 重置重试计数
                        else:
                            # 使用指数退避策略，每次失败后等待时间增加
                            wait_time = min(10 * (2 ** (retry_count - 1)), 60)  # 最长等待60秒
                            time.sleep(wait_time)

        except Exception as e:
            logging.error(f"摄像头 {camera_id} 线程发生错误: {str(e)}")
        finally:
            # 确保在线程结束时释放资源
            if camera_id in self.camera_executors:
                try:
                    self.camera_executors[camera_id].shutdown(wait=False)
                except:
                    pass

    def start_camera_monitoring(self, camera_config: dict):
        """
        启动单个摄像头的监控

        Args:
            camera_config: 摄像头配置字典，包含:
                - camera_id: 摄像头ID
                - video_path: 视频路径
                - threshold: 覆盖率阈值
                - frame_interval_all: 帧间隔时间
                - system_type: 系统类型
        """
        camera_id = camera_config['camera_id']
        self.stop_flags[camera_id] = False
        
        # 添加更详细的启动日志
        logging.info(f"启动摄像头 {camera_id} 监控 - 视频路径: {camera_config.get('video_path')}, 系统类型: {camera_config.get('system_type')}")
        logging.debug(f"摄像头 {camera_id} 详细配置: 阈值={camera_config.get('threshold')}, 帧间隔={camera_config.get('frame_interval_all')}秒")
        
        thread = threading.Thread(
            target=self.process_single_camera,
            args=(camera_config,),
            name=f"Camera-{camera_id}"
        )
        self.camera_threads[camera_id] = thread
        thread.start()
        logging.info(f"摄像头 {camera_id} 监控线程已启动")

    def stop_camera_monitoring(self, camera_id: str):
        """
        安全停止单个摄像头的监控

        Args:
            camera_id: 要停止的摄像头ID

        说明:
            - 设置停止标志
            - 等待线程结束
            - 释放摄像头资源
            - 关闭线程池
        """
        try:
            # 1. 首先设置停止标志
            logging.info(f"正在停止摄像头 {camera_id} 监控...")
            self.stop_flags[camera_id] = True
            
            # 2. 等待线程结束
            if camera_id in self.camera_threads:
                thread = self.camera_threads[camera_id]
                if thread.is_alive():
                    logging.debug(f"等待摄像头 {camera_id} 线程结束...")
                    thread.join(timeout=5)  # 等待最多5秒
                    if thread.is_alive():
                        logging.warning(f"摄像头 {camera_id} 线程在5秒内未结束")
                    else:
                        logging.debug(f"摄像头 {camera_id} 线程已正常结束")
                del self.camera_threads[camera_id]

            # 3. 释放摄像头资源
            if camera_id in self.camera_captures:
                cap = self.camera_captures[camera_id]
                if cap is not None:
                    logging.debug(f"释放摄像头 {camera_id} 资源...")
                    cap.release()
                del self.camera_captures[camera_id]

            # 4. 关闭线程池
            if camera_id in self.camera_executors:
                executor = self.camera_executors[camera_id]
                if executor is not None:
                    logging.debug(f"关闭摄像头 {camera_id} 线程池...")
                    executor.shutdown(wait=True)  # 等待所有任务完成
                del self.camera_executors[camera_id]

            logging.info(f"摄像头 {camera_id} 已安全停止")
        except Exception as e:
            logging.error(f"停止摄像头 {camera_id} 监控时发生错误: {str(e)}")

    def safe_cleanup_resources(self):
        """
        安全清理所有系统资源

        执行操作:
            1. 停止所有摄像头监控
            2. 清理所有线程资源
            3. 释放所有摄像头资源
        """
        try:
            for camera_id in list(self.camera_threads.keys()):
                try:
                    self.stop_camera_monitoring(camera_id)
                except Exception as e:
                    logging.error(f"停止摄像头 {camera_id} 时发生错误: {str(e)}")

            self.camera_threads.clear()
            logging.info("所有资源已清理")
        except Exception as e:
            logging.error(f"清理资源时发生错误: {str(e)}")

    def check_api_status(self):
        """
        检查API服务器状态

        Returns:
            bool: True表示API服务正常运行，False表示服务异常
        """
        try:
            # 使用 get_cameras_config 方法获取摄像头配置
            # cameras_config = self.get_cameras_config()
            cameras_config = get_cameras_config()
            # 如果能获取到有效的摄像头置，则认为API正常行
            return bool(cameras_config)
        except Exception as e:
            logging.error(f"检查API状态时发生错误: {str(e)}")
            return False

    def check_camera_health(self, camera_id: str) -> bool:
        """
        检查摄像头健康状态

        Args:
            camera_id: 摄像头ID

        Returns:
            bool: True表示摄像头正常工作，False表示摄像头异常

        检查项目:
            1. 摄像头是否在注册列表中
            2. 摄像头对象是否有效
            3. 摄像头是否能正常读取帧
        """
        if camera_id not in self.camera_captures:
            return False

        cap = self.camera_captures[camera_id]
        if not cap or not cap.isOpened():
            return False

        ret, _ = cap.read()
        return ret

    # def get_cameras_config(self) -> List[Dict]:
    #     """获取摄像头配置列表

    #     Returns:
    #         List[Dict]: 摄像头配置列表
    #     """
    #     try:
    #         # 使用api1_test.py中的方式获取设备列表
    #         result = get_device_list(main_type="cam")
    #         if not result:
    #             logging.error("获取设备列表失败")
    #             return []

    #         # 处理设备数据并构建配置
    #         camera_configs = process_device_data(result)
    #         logging.info(f"成功获取 {len(camera_configs)} 个摄像头配置")
    #         return camera_configs

    #     except Exception as e:
    #         logging.error(f"获取摄像头配置时发生错误: {str(e)}")
    #         return []

    def run(self):
        """
        运行监控系统的主循环

        主要功能:
            1. 定时执行数据清理(凌晨2点)
            2. 检查API服务器状态
            3. 管理摄像头的启动和停止
            4. 错误处理和系统恢复

        错误处理:
            - consecutive_errors: 连续错误计数
            - max_consecutive_errors: 最大允许连续错误次数(5次)
            - 超过最大错误次数后系统将重启
        """
        consecutive_errors = 0
        max_consecutive_errors = 5
        current_configs = {}  # 存储当前的配置

        while self.system_running:
            try:
                # 每天凌晨2点执行清理操作
                current_time = datetime.now()
                if current_time.hour == 2 and current_time.minute == 0:
                    self.cleanup_manager.cleanup_old_images()

                # 查API服务器状态并获取摄像头配置
                # cameras_config = self.get_cameras_config()
                cameras_config = get_cameras_config()
                self.is_api_running = bool(cameras_config)

                if self.is_api_running:
                    success_msg = f"成功获取 {len(cameras_config)} 个摄像头配置"
                    print(f"{time.strftime('%Y-%m-%d %H:%M:%S')} {success_msg}")
                    logging.info(success_msg)

                    # 将配置转换为字典，方便比较
                    # 从配置文件中获取需要过滤的system_type列表
                    filtered_system_types = self.config.get('system', {}).get('camera_filter', {}).get('filtered_system_types', [])
                    if not filtered_system_types:  # 如果配置为空，使用默认值
                        filtered_system_types = ['system_prompt_sedimentation_all', 'system_prompt_robot_sedimentation', 'system_prompt_slag_weir_gate']
                        logging.warning("未在配置文件中找到过滤列表，使用默认值")
                    
                    new_configs = {
                        cfg['camera_id']: cfg
                        for cfg in cameras_config
                        if cfg.get('system_type') not in filtered_system_types  # 过滤掉不需要在此处理的摄像头类型
                    }

                    # 记录被忽略的摄像头
                    ignored_cameras = [cfg['camera_id'] for cfg in cameras_config 
                                      if cfg.get('system_type') in filtered_system_types]
                    if ignored_cameras:
                        logging.info(f"以下摄像头因system_type在过滤列表中被忽略: {', '.join(ignored_cameras)}")

                    # 处理每个摄像头
                    for camera_id, new_config in new_configs.items():
                        try:
                            old_config = current_configs.get(camera_id)
                            
                            # 如果是新摄像头或配置有变化，需要重启线程
                            if old_config != new_config:
                                if camera_id in self.camera_threads:
                                    stop_msg = f"检测到摄像头 {camera_id} 配置变化，正在重启..."
                                    print(f"{time.strftime('%Y-%m-%d %H:%M:%S')} {stop_msg}")
                                    logging.warning(stop_msg)
                                    self.stop_camera_monitoring(camera_id)
                                
                                start_msg = f"启动摄像头 {camera_id} 的监控"
                                print(f"{time.strftime('%Y-%m-%d %H:%M:%S')} {start_msg}")
                                logging.info(start_msg)
                                self.start_camera_monitoring(new_config)

                            """设置从数据库中获取的覆盖率进行上报,数据库中没有的不进行上报"""
                            video_id = new_config['video_id']
                            camera_id_coverage_rate = self.data_publisher.get_coverage_rate(camera_id)
                            if camera_id_coverage_rate != '-' and camera_id_coverage_rate != 1 and camera_id_coverage_rate != 99:
                                publish_foam_data(video_id, camera_id_coverage_rate)

                        except Exception as e:
                            error_msg = f"处理摄像头 {camera_id} 时发生错误: {str(e)}"
                            print(f"{time.strftime('%Y-%m-%d %H:%M:%S')} {error_msg}")
                            logging.error(error_msg)

                    # 停止已移除的摄像头
                    for camera_id in list(self.camera_threads.keys()):
                        if camera_id not in new_configs:
                            stop_msg = f"停止摄像头 {camera_id} 的监控"
                            print(f"{time.strftime('%Y-%m-%d %H:%M:%S')} {stop_msg}")
                            logging.info(stop_msg)
                            self.stop_camera_monitoring(camera_id)

                    # 更新当前配置
                    current_configs = new_configs

                else:
                    wait_msg = "等待API服务器响应..."
                    print(f"{time.strftime('%Y-%m-%d %H:%M:%S')} {wait_msg}")
                    logging.info(wait_msg)

                # 系统待机
                time.sleep(self.check_interval)

                consecutive_errors = 0  # 重置错误计数
            except Exception as e:
                consecutive_errors += 1
                logging.error(f"运行时发生错误 ({consecutive_errors}/{max_consecutive_errors}): {str(e)}")

                if consecutive_errors >= max_consecutive_errors:
                    logging.critical("连续错误次数过多，系统将重启...")
                    self.safe_cleanup_resources()
                    time.sleep(30)  # 等待系统稳定
                    consecutive_errors = 0

    def graceful_shutdown(self):
        """
        优雅关闭系统

        执行操作:
            1. 设置系统停止标志
            2. 清理所有资源
            3. 记录关闭日志
        """
        logging.info("正在关闭系统...")
        self.system_running = False
        self.safe_cleanup_resources()
        logging.info("系统已安全关闭")


def start(test_path=None):
    try:
        # 加载环境变量,验证环境变量是否存在
        EnvManager.load_env()

        # 设置日志
        setup_logging()

        # 创建并运行监控系统
        monitoring_system = VideoMonitoringSystem()
        # ======================== 开启测试模式 ======================== #
        # 从配置文件中获取测试模式配置
        test_mode_config = monitoring_system.config.get('system', {}).get('test_mode_config', {})
        test_mode_enabled = test_mode_config.get('enabled', False)
        
        # 如果命令行参数提供了test_path，优先使用命令行参数
        # 否则使用配置文件中的test_path
        if test_path is None:
            test_path = test_mode_config.get('test_path', '')
            
        # 如果test_path为空但测试模式已启用，使用默认测试路径
        if test_mode_enabled and not test_path:
            test_path = test_mode_config.get('default_test_path', '')
            
        # 如果测试模式已启用且有有效的测试路径，启动测试模式
        if test_mode_enabled and test_path:
            logging.info(f"根据配置文件启动测试模式，测试路径: {test_path}")
            monitoring_system.start_test_mode(test_path)
        # ======================== 开启测试模式 ======================== #
        monitoring_system.run()
    except KeyboardInterrupt:
        logging.info("收到终止信号，正在安全关闭系统...")
        monitoring_system.graceful_shutdown()
    except Exception as e:
        logging.error(f"系统发生严重错误: {str(e)}")
        if 'monitoring_system' in locals():
            monitoring_system.graceful_shutdown()



