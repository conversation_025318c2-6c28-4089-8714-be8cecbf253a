# 获取设备点位信息模块
# 用于从API获取和处理设备点位数据

import json
import os
import logging
from typing import Dict, Any

import requests
import yaml
from requests.exceptions import RequestException
from config_file import config
from server.utils.logger import setup_logging

# 配置日志
setup_logging()
logger = logging.getLogger(__name__)


# 加载配置

api_config = config.env['api']['development']


class APIClient:
    """API客户端类

    用于处理与设备点位相关的API请求

    Attributes:
        base_url (str): API基础URL，格式为 http://{host}:{port}/env-api
        headers (dict): 请求头，包含认证token
    """

    def __init__(self):
        """初始化API客户端

        设置基础URL和认证头信息
        """
        self.base_url = f"http://{api_config['host']}:{api_config['port']}/env-api"
        self.headers = {
            "Authorization": api_config['token']
        }

    def get_device_points(self, device_id: str, bind_status: str = "1", query_value: str = "true") -> Dict[str, Any]:
        """获取设备点位信息

        Args:
            device_id (str): 设备ID
            bind_status (str): 绑定状态，默认"1"表示已绑定
            query_value (str): 是否查询值，默认"true"

        Returns:
            Dict[str, Any]: API响应数据，包含设备点位信息

        Raises:
            RequestException: 当API请求失败时
            json.JSONDecodeError: 当响应数据解析失败时
        """
        endpoint = "/device_point/list"
        params = {
            "device_id": device_id,
            "bind_status": bind_status,
            "query_value": query_value
        }

        try:
            response = requests.get(
                f"{self.base_url}{endpoint}",
                headers=self.headers,
                params=params
            )
            response.raise_for_status()
            return response.json()

        except (RequestException, json.JSONDecodeError) as e:
            logger.error(f"请求出错: {str(e)}")
            raise

    def format_point_data(self, data: Dict) -> Dict:
        """格式化点位数据

        将API返回的原始点位数据转换为标准格式

        Args:
            data (Dict): 原始点位数据

        Returns:
            Dict: 格式化后的点位数据，其中：
                - 键: 根据配置文件中的point_mapping映射转换
                - 值: 转换为浮点数并四舍五入到1位小数
                - 特殊处理:
                    * 空值或'-'转换为0
                    * 确保所有映射键都存在，不存在的设为0

        数据处理流程:
            1. 检查输入数据有效性
            2. 获取配置文件中的点位映射
            3. 遍历原始数据进行转换
            4. 处理特殊值和缺失值
            5. 确保输出数据完整性
        """
        result = {}
        if not data or 'data' not in data:
            return result

        # 从配置文件获取点位映射
        point_mapping = config.env['point_info']['point_mapping']

        for point in data['data']:
            point_code = point.get('remark')
            if point_code in point_mapping:
                value = point.get('value')
                # 如果值是'-'或空，则设置为0
                if value in ['-', '', None]:
                    value = 0
                else:
                    # 否则转换为浮点数并四舍五入
                    value = round(float(value), 1)
                key = point_mapping[point_code]
                result[key] = value

        # 确保所有键都存在，如果不存在则设置为0
        for output_key in point_mapping.values():
            if output_key not in result:
                result[output_key] = 0

        return result

    def get_point_history(self, point_id: str, start_time: str, end_time: str, 
                         time_type: str = "hour", page_size: int = 10, page_num: int = 1) -> Dict[str, Any]:
        """获取设备点位历史数据
        
        Args:
            point_id (str): 设备点位ID
            start_time (str): 开始时间，格式如 "2024-04-16 17:00:00"
            end_time (str): 结束时间，格式如 "2024-04-16 23:00:00"
            time_type (str, optional): 时间类型，可选值为 min(分钟)、hour(小时)、day(天)，默认为"hour"
            page_size (int, optional): 每页记录数，默认为10
            page_num (int, optional): 页码，默认为1
            
        Returns:
            Dict[str, Any]: API响应数据，包含设备点位历史数据
            
        Raises:
            RequestException: 当API请求失败时
            json.JSONDecodeError: 当响应数据解析失败时
        """
        endpoint = "/dev_data_point/detail"
        params = {
            "point_id": point_id,
            "start_time": start_time,
            "end_time": end_time,
            "time_type": time_type,
            "page_size": page_size,
            "page_num": page_num
        }
        
        try:
            response = requests.get(
                f"{self.base_url}{endpoint}",
                headers=self.headers,
                params=params
            )
            response.raise_for_status()
            return response.json()
            
        except (RequestException, json.JSONDecodeError) as e:
            logger.error(f"获取历史数据请求出错: {str(e)}")
            raise


def main():
    """主函数

    用于测试设备点位信息的获取和格式化

    执行流程:
        1. 创建API客户端
        2. 获取原始点位数据
        3. 格式化数据
        4. 打印结果
    """
    try:
        client = APIClient()
        raw_data = client.get_device_points(device_id="4047")
        logger.info(f"获取设备点位数据: {raw_data}")
        # formatted_data = client.format_point_data(raw_data)

        # logger.info("格式化后的数据:")
        # logger.info(json.dumps(formatted_data, ensure_ascii=False, indent=2))

        # 测试获取历史数据
        try:
            logger.info("获取历史数据示例:")
            history_data = client.get_point_history(
                point_id="28b23000-11de-4c47-9da4-69e4aab025cb",
                start_time="2025-04-21 00:00:00",
                end_time="2025-04-22 18:37:36",
                time_type="day",
                page_size=10,
                page_num=1
            )
            logger.info("历史数据响应:")
            logger.info(json.dumps(history_data, ensure_ascii=False, indent=2))
        except Exception as history_err:
            logger.error(f"获取历史数据失败: {str(history_err)}")

    except Exception as e:
        logger.error(f"执行出错: {str(e)}")


if __name__ == "__main__":
    main()
