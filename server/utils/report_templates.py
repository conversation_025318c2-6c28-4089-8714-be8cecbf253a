# ----------,--------------------------------------------- #
#                        报告生成模块重构                        #
# -------------------------------------------------------- #
import os 
import sys
import logging
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from server.utils import pg_tool
from datetime import datetime
import json
from typing import List, Dict, Any, Tuple
from openai import OpenAI
from llms.config_report_day import SYSTEM_REPORT_DAY
from llms.config_report_monthly import SYSTEM_REPORT_MONTHLY
from config_file import config
from server.utils.get_cameras_info import get_cameras_config
from server.utils.logger import setup_logging
from collections import defaultdict
import statistics
from calendar import monthrange

# 配置日志
setup_logging()
logger = logging.getLogger(__name__)


# -------------------------------------------------------- #
#                        公共工具类                           #
# -------------------------------------------------------- #

class ReportDataProcessor:
    """报告数据处理公共类"""
    
    @staticmethod
    def preprocess_device_data(data):
        """
        对设备数据进行预处理，将数据分为三组
        
        Args:
            data (list): 设备数据列表
            
        Returns:
            tuple: 包含三个列表的元组 (warning_data, non_warning_data, transition_data)
        """
        if not data:
            return [], [], []
        
        # 按时间戳升序排序
        sorted_data = sorted(data, key=lambda x: x['timestamp'], reverse=False)
        
        warning_data = []
        non_warning_data = []
        transition_data = []
        
        # 上一条记录的告警状态，初始为None
        prev_alarm_status = None
        
        for record in sorted_data:
            current_alarm_status = record.get('alarm_status')
            
            # 分类数据
            if current_alarm_status == 'WARNING':
                warning_data.append(record)
                
                # 检查是否从非WARNING状态转变为WARNING状态
                if prev_alarm_status is not None and prev_alarm_status != 'WARNING':
                    transition_data.append(record)
            else:
                non_warning_data.append(record)
            
            # 更新上一条记录的告警状态
            prev_alarm_status = current_alarm_status
        
        return warning_data, non_warning_data, transition_data
    
    @staticmethod
    def chunk_data(data: List[Dict], chunk_size: int = 5) -> List[List[Dict]]:
        """将数据按指定大小分组"""
        return [data[i:i + chunk_size] for i in range(0, len(data), chunk_size)]
    
    @staticmethod
    def get_device_name(camera_id):
        """获取设备名称"""
        cameras_config = get_cameras_config()
        for camera_config in cameras_config:
            if camera_config.get('camera_id') == camera_id:
                return camera_config.get('device_name', '未知设备')
        return f"设备{camera_id}"
    
    @staticmethod
    def summarize_normal_data_simple(normal_data: List[Dict]) -> str:
        """
        简化的正常数据总结 - 只提供基本统计
        
        Args:
            normal_data (List[Dict]): 正常数据列表
            
        Returns:
            str: 简化的正常数据统计
        """
        if not normal_data:
            return "无正常运行数据记录"
        
        # 按时间排序
        sorted_data = sorted(normal_data, key=lambda x: x['timestamp'])
        start_time = sorted_data[0]['timestamp']
        end_time = sorted_data[-1]['timestamp']
        total_time_span = (end_time - start_time).total_seconds() / 3600  # 转换为小时
        
        return f"正常运行记录{len(normal_data)}条，时间跨度{total_time_span:.1f}小时"


class LLMAnalyzer:
    """大模型分析类"""
    
    def __init__(self):
        self.client = OpenAI(
            api_key=config.env_vars.get("GLM_API_KEY"),
            base_url=config.env_vars.get("GLM_BASE_URL")
        )
        self.model = config.env_vars.get("GLM_MODEL")

    def analyze_warning_data(self, data_summary: str, system_prompt_type: str = 'analysis') -> str:
        """分析告警数据"""
        prompt = f"""
                    你的目的是对以下设备告警数据进行总结归纳，重点关注：
                    1. 告警模式和规律（时间分布、频率变化、持续时间等）
                    2. 影响评估（对生产、安全、效率的影响）
                    尽量的简明扼要，不需要给出建议分具体原因等等的分析,只需要将重要的实体\关系等等进行提取和汇总.
                    告警数据：
                    {data_summary}
                    3. 生成的内容最长不要超过300字.
                """
        return self._call_llm(prompt, system_prompt_type)
    
    def analyze_transition_data(self, data_summary: str, system_prompt_type: str = 'analysis') -> str:
        """分析状态转换数据"""
        prompt = f"""你的目的是对以下设备状态转换数据进行总结归纳，重点关注：
                    1. 状态转换的触发条件和模式识别
                    2. 转换时机的规律性分析（是否有固定时段、周期性等）
                    3. 转换前的预警信号和征兆识别
                    尽量的简明扼要，不需要给出建议分具体原因等等的分析,只需要将重要的实体\关系等等进行提取和汇总.

                    状态转换数据：
                    {data_summary}
                    4. 生成的内容最长不要超过300字.
                """        
        return self._call_llm(prompt, system_prompt_type)
    
    def generate_full_report(self, analysis_data: dict, report_type: str = 'monthly') -> str:
        """生成完整报告"""
        if report_type == 'monthly':
            system_prompt = SYSTEM_REPORT_MONTHLY.get("system_prompt_report_monthly")
        else:
            system_prompt = SYSTEM_REPORT_DAY.get("system_prompt_report_day")
            
        return self._call_llm(str(analysis_data), 'all', system_prompt)
    
    def _call_llm(self, prompt: str, system_prompt_type: str, custom_system_prompt: str = None) -> str:
        """调用大模型API"""
        try:
            if custom_system_prompt:
                system_prompt = custom_system_prompt
            elif system_prompt_type == 'summary':
                system_prompt = SYSTEM_REPORT_DAY.get("system_prompt_report_day_summary", 
                                                     "你是一个专业的设备监控分析师，擅长分析设备运行数据并提供专业的见解,不要超过200字。")
            elif system_prompt_type == 'analysis':
                system_prompt = "你是一个专业的设备监控分析师，擅长分析设备运行数据并提供专业的见解,不要超过200字。"
            elif system_prompt_type == 'scene':
                system_prompt = SYSTEM_REPORT_MONTHLY.get("system_prompt_report_monthly_scene",
                                                         "你是一个专业的设备监控分析师，擅长分析场景级别的设备运行数据并提供专业的见解,不要超过200字。")
            elif system_prompt_type == 'scene_summary':
                system_prompt = SYSTEM_REPORT_MONTHLY.get("system_prompt_report_monthly_scene_summary",
                                                         "请对场景报告进行简洁总结。")
            else: 
                system_prompt = "你是一个专业的设备监控分析师，擅长分析设备运行数据并提供专业的见解,不要超过200字。"
                # 添加当前时间
                report_generation_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                system_prompt = system_prompt + f"\n当前时间: {report_generation_time}"
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=4000
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"调用大模型API出错: {e}")
            return f"大模型分析失败: {str(e)}\n原始数据总结: {prompt}"


class DataAnalyzer:
    """数据分析类"""
    
    @staticmethod
    def analyze_warning_chunk(warning_chunk: List[Dict]) -> str:
        """分析一组告警数据"""
        if not warning_chunk:
            return ""
        
        # 按时间排序确保数据是有序的
        sorted_data = sorted(warning_chunk, key=lambda x: x['timestamp'])
        
        # 计算告警时间段
        # 如果两个告警记录之间的时间间隔超过5分钟，就认为它们属于不同的告警时间段
        time_gap_threshold = 5 * 60  # 5分钟，单位为秒
        warning_periods = []
        
        if len(sorted_data) == 1:
            warning_periods = [(sorted_data[0]['timestamp'], sorted_data[0]['timestamp'])]
        else:
            period_start = sorted_data[0]['timestamp']
            prev_time = period_start
            
            for i in range(1, len(sorted_data)):
                current_time = sorted_data[i]['timestamp']
                time_diff = (current_time - prev_time).total_seconds()
                
                if time_diff > time_gap_threshold:
                    warning_periods.append((period_start, prev_time))
                    period_start = current_time
                
                prev_time = current_time
            
            warning_periods.append((period_start, sorted_data[-1]['timestamp']))
        
        # 统计覆盖级别
        coverage_levels = {}
        for record in warning_chunk:
            level = record.get('coverage_level', 'UNKNOWN')
            coverage_levels[level] = coverage_levels.get(level, 0) + 1
        
        # 提取分析详情和调整建议
        analysis_details = set(record.get('analysis_detail', '') for record in warning_chunk if record.get('analysis_detail'))
        adjustment_suggestions = set(record.get('adjustment_suggestion', '') for record in warning_chunk if record.get('adjustment_suggestion'))
        
        # 统计故障原因类型
        failure_types = {}
        for record in warning_chunk:
            for failure_type in record.get('failure_reasons_type', []):
                failure_types[failure_type] = failure_types.get(failure_type, 0) + 1
        
        # 构建分析文本
        if len(warning_periods) == 1:
            start_time = warning_periods[0][0]
            end_time = warning_periods[0][1]
            duration_minutes = (end_time - start_time).total_seconds() / 60
            analysis = f"在{start_time.strftime('%H:%M:%S')}到{end_time.strftime('%H:%M:%S')}期间，设备出现告警状态，持续约{duration_minutes:.1f}分钟。"
        else:
            period_texts = []
            total_duration_minutes = 0
            for start, end in warning_periods:
                duration = (end - start).total_seconds() / 60
                total_duration_minutes += duration
                period_texts.append(f"{start.strftime('%H:%M:%S')}到{end.strftime('%H:%M:%S')}（约{duration:.1f}分钟）")
            
            analysis = f"设备在多个时间段内出现告警状态：{', '.join(period_texts)}，总计约{total_duration_minutes:.1f}分钟。"
        
        # 添加覆盖级别统计
        coverage_info = []
        for level, count in coverage_levels.items():
            percentage = (count / len(warning_chunk)) * 100
            coverage_info.append(f"{level}级别占比{percentage:.1f}%")
        
        if coverage_info:
            analysis += f" 覆盖级别统计：{', '.join(coverage_info)}。"
        
        # 添加故障原因类型统计
        if failure_types:
            main_failures = sorted(failure_types.items(), key=lambda x: x[1], reverse=True)[:3]
            failure_info = [f"{failure_type}({count}次)" for failure_type, count in main_failures]
            analysis += f" 主要故障类型：{', '.join(failure_info)}。"
        
        # 添加分析详情和调整建议的摘要
        if analysis_details:
            analysis += f" 分析详情：{'; '.join(list(analysis_details)[:3])}。"
        
        if adjustment_suggestions:
            analysis += f" 调整建议：{'; '.join(list(adjustment_suggestions)[:3])}。"
        
        return analysis
    
    @staticmethod
    def analyze_transition_chunk(transition_chunk: List[Dict]) -> str:
        """分析一组状态转变数据"""
        if not transition_chunk:
            return ""
        
        # 按时间排序
        sorted_data = sorted(transition_chunk, key=lambda x: x['timestamp'])
        
        # 分析转变事件
        results = []
        
        # 统计转变发生的时间分布
        hour_distribution = {}
        coverage_level_counts = {}
        failure_reasons_counts = {}
        
        for record in sorted_data:
            timestamp = record['timestamp']
            hour = timestamp.hour
            hour_distribution[hour] = hour_distribution.get(hour, 0) + 1
            
            coverage_level = record.get('coverage_level', 'UNKNOWN')
            coverage_level_counts[coverage_level] = coverage_level_counts.get(coverage_level, 0) + 1
            
            for reason in record.get('failure_reasons_type', []):
                failure_reasons_counts[reason] = failure_reasons_counts.get(reason, 0) + 1
            
            analysis_detail = record.get('analysis_detail', '无详情')
            adjustment_suggestion = record.get('adjustment_suggestion', '无建议')
            failure_types = ', '.join(record.get('failure_reasons_type', ['未知']))
            
            # 为每个转变事件生成描述
            event_text = f"在{timestamp.strftime('%H:%M:%S')}，设备从正常状态转变为告警状态，"
            event_text += f"覆盖级别为{coverage_level}，"
            
            if failure_types:
                event_text += f"故障类型为{failure_types}，"
            
            event_text += f"分析详情：{analysis_detail}，调整建议：{adjustment_suggestion}。"
            results.append(event_text)
        
        # 生成整体摘要
        summary = ""
        
        # 添加时间分布摘要
        if hour_distribution:
            peak_hours = sorted(hour_distribution.items(), key=lambda x: x[1], reverse=True)[:3]
            peak_hours_text = []
            for hour, count in peak_hours:
                percentage = (count / len(sorted_data)) * 100
                peak_hours_text.append(f"{hour}时({percentage:.1f}%)")
            
            summary += f"状态转变主要发生在以下时段：{', '.join(peak_hours_text)}。"
        
        # 添加覆盖级别摘要
        if coverage_level_counts:
            main_levels = sorted(coverage_level_counts.items(), key=lambda x: x[1], reverse=True)
            level_texts = []
            for level, count in main_levels:
                percentage = (count / len(sorted_data)) * 100
                level_texts.append(f"{level}({percentage:.1f}%)")
            
            summary += f" 状态转变时的覆盖级别分布：{', '.join(level_texts)}。"
        
        # 添加故障原因摘要
        if failure_reasons_counts:
            main_reasons = sorted(failure_reasons_counts.items(), key=lambda x: x[1], reverse=True)[:3]
            reason_texts = []
            for reason, count in main_reasons:
                percentage = (count / len(sorted_data)) * 100
                reason_texts.append(f"{reason}({percentage:.1f}%)")
            
            summary += f" 主要故障原因：{', '.join(reason_texts)}。"
        
        # 组合整体摘要和详细事件
        if summary:
            return summary + "\n\n详细转变事件：\n" + "\n".join(results)
        else:
            return "\n".join(results)


# -------------------------------------------------------- #
#                        日报生成类                           #
# -------------------------------------------------------- #

class DailyReportGenerator:
    """日报生成器"""
    
    def __init__(self):
        self.data_processor = ReportDataProcessor()
        self.llm_analyzer = LLMAnalyzer()
        self.data_analyzer = DataAnalyzer()
    
    def generate_device_report(self, camera_id, reference_date=None):
        """
        生成设备报告，包含三组数据：告警数据、非告警数据和状态转变数据
        
        Args:
            camera_id (str): 设备ID/摄像头ID
            reference_date (str, optional): 参考日期，格式为'YYYY-MM-DD'，默认为当前日期
            
        Returns:
            dict: 包含三组数据的字典
        """
        # 如果没有提供参考日期，使用当前日期
        if reference_date is None:
            reference_date = datetime.now().strftime('%Y-%m-%d')
        
        # 获取设备数据
        device_data = pg_tool.query_device_today(camera_id, reference_date)
        
        # 获取设备名称
        device_name = self.data_processor.get_device_name(camera_id)
        
        # 预处理数据
        warning_data, non_warning_data, transition_data = self.data_processor.preprocess_device_data(device_data)
        
        # 处理正常数据 - 只进行简单统计
        normal_summary = self.data_processor.summarize_normal_data_simple(non_warning_data)
        
        # 处理告警数据
        warning_analysis = self._analyze_warning_data(warning_data)
        
        # 处理状态转变数据
        transition_analysis = self._analyze_transition_data(transition_data)
        
        # 生成完整报告
        report = {
            "raw_data": {
                "warning_data": warning_data,
                "non_warning_data": non_warning_data,
                "transition_data": transition_data
            },
            "analysis": {
                "normal_analysis": normal_summary,
                "warning_analysis": warning_analysis,
                "transition_analysis": transition_analysis
            },
            "summary": {
                "normal_count": len(non_warning_data),
                "warning_count": len(warning_data),
                "transition_count": len(transition_data)
            },
            "device_info": {
                "camera_id": camera_id,
                "device_name": device_name
            }
        }
        
        return report
    
    def _analyze_warning_data(self, warning_data):
        """分析告警数据"""
        if not warning_data:
            return "今日无告警数据。"
        
        if len(warning_data) > 20:
            # 如果告警数据超过20条，按5条一组进行分析
            warning_chunks = self.data_processor.chunk_data(warning_data, 5)
            chunk_analyses = []
            
            for i, chunk in enumerate(warning_chunks):
                chunk_analysis = self.data_analyzer.analyze_warning_chunk(chunk)
                chunk_analyses.append(f"告警组 {i+1}:\n{chunk_analysis}")
            
            # 将所有分组分析结果合并
            all_chunk_analyses = "\n\n".join(chunk_analyses)
            
            # 使用大模型对所有分组进行总结
            return self.llm_analyzer.analyze_warning_data(all_chunk_analyses)
        else:
            # 如果告警数据不超过20条，直接分析
            warning_summary = self.data_analyzer.analyze_warning_chunk(warning_data)
            return self.llm_analyzer.analyze_warning_data(warning_summary)
    
    def _analyze_transition_data(self, transition_data):
        """分析状态转换数据"""
        if not transition_data:
            return "今日无状态转变数据。"
        
        if len(transition_data) > 10:
            # 如果状态转变数据超过10条，按5条一组进行分析
            transition_chunks = self.data_processor.chunk_data(transition_data, 5)
            chunk_analyses = []
            
            for i, chunk in enumerate(transition_chunks):
                chunk_analysis = self.data_analyzer.analyze_transition_chunk(chunk)
                chunk_analyses.append(f"状态转变组 {i+1}:\n{chunk_analysis}")
            
            # 将所有分组分析结果合并
            all_chunk_analyses = "\n\n".join(chunk_analyses)
            
            # 使用大模型对所有分组进行总结
            return self.llm_analyzer.analyze_transition_data(all_chunk_analyses)
        else:
            # 如果状态转变数据不超过10条，直接分析
            transition_summary = self.data_analyzer.analyze_transition_chunk(transition_data)
            return self.llm_analyzer.analyze_transition_data(transition_summary)
    
    def generate_daily_report(self, camera_id, reference_date=None):
        """
        生成每日报告的完整文本
        
        Args:
            camera_id (str): 设备ID/摄像头ID
            reference_date (str, optional): 参考日期，格式为'YYYY-MM-DD'，默认为当前日期
            
        Returns:
            tuple: (device_name, normal_count, warning_count, transition_count, 
                   normal_analysis, warning_analysis, transition_analysis, report_text_export)
        """
        # 获取报告数据
        report = self.generate_device_report(camera_id, reference_date)
        
        # 获取设备名称
        device_name = report['device_info']['device_name']
        
        # 格式化日期
        if reference_date is None:
            reference_date = datetime.now().strftime('%Y-%m-%d')
        
        # 构建报告文本
        report_text = f"# 设备 {camera_id} 日报 ({reference_date}) 巡检场景: {device_name}\n\n"
        
        # 添加摘要信息
        report_text += "## 数据摘要\n\n"
        report_text += f"- 正常记录数: {report['summary']['normal_count']}\n"
        report_text += f"- 告警记录数: {report['summary']['warning_count']}\n"
        report_text += f"- 状态转变记录数: {report['summary']['transition_count']}\n\n"
        
        # 添加正常运行分析
        report_text += "## 正常运行分析\n\n"
        report_text += f"{report['analysis']['normal_analysis']}\n\n"
        
        # 添加告警分析
        report_text += "## 告警分析\n\n"
        report_text += f"{report['analysis']['warning_analysis']}\n\n"
        
        # 添加状态转变分析
        report_text += "## 状态转变分析\n\n"
        report_text += f"{report['analysis']['transition_analysis']}\n\n"
        
        # 使用LLM生成最终报告
        report_text_export = self.llm_analyzer.generate_full_report(report_text, 'daily')
        
        # 清理报告格式
        report_text_export = self._clean_report_format(report_text_export)
        
        normal_count = report['summary']['normal_count']
        warning_count = report['summary']['warning_count']
        transition_count = report['summary']['transition_count']
        normal_analysis = report['analysis']['normal_analysis']
        warning_analysis = self._clean_report_format(report['analysis']['warning_analysis'])
        transition_analysis = self._clean_report_format(report['analysis']['transition_analysis'])
        
        return device_name, normal_count, warning_count, transition_count, normal_analysis, warning_analysis, transition_analysis, report_text_export
    
    def _clean_report_format(self, text):
        """清理报告格式，去除多余的markdown标记"""
        if isinstance(text, str):
            if text.startswith('```') and text.endswith('```'):
                return text[3:-3]
            elif text.startswith('```\n') and text.endswith('\n```'):
                return text[4:-4]
        return text


# -------------------------------------------------------- #
#                        月报生成类                           #
# -------------------------------------------------------- #

class MonthlyReportGenerator:
    """月报生成器"""
    
    def __init__(self):
        self.data_processor = ReportDataProcessor()
        self.llm_analyzer = LLMAnalyzer()
        self.data_analyzer = DataAnalyzer()
    
    def query_device_month(self, camera_id, year, month):
        """
        查询指定设备在指定年月的所有数据
        
        Args:
            camera_id (str): 设备ID
            year (int): 年份
            month (int): 月份
            
        Returns:
            list: 包含设备数据的列表
        """
        # 计算月份的开始和结束日期
        start_date = f"{year}-{month:02d}-01"
        last_day = monthrange(year, month)[1]
        end_date = f"{year}-{month:02d}-{last_day}"
        
        conn = pg_tool.connect_db()
        if not conn:
            return []
        
        try:
            cur = conn.cursor()
            
            # 优化查询语句 - 只查询月报分析需要的字段
            query_sql = """
                SELECT timestamp, camera_id, alarm_status, 
                       analysis_detail, coverage_level, 
                       adjustment_suggestion, failure_reasons_type
                FROM frame_analysis
                WHERE camera_id = %s 
                AND DATE(timestamp) >= %s 
                AND DATE(timestamp) <= %s
                ORDER BY timestamp ASC
            """
            
            cur.execute(query_sql, (camera_id, start_date, end_date))
            results = cur.fetchall()
            
            # 转换为字典列表 - 只包含需要的字段
            data = []
            for row in results:
                data.append({
                    'timestamp': row[0],
                    'camera_id': row[1],
                    'alarm_status': row[2],
                    'analysis_detail': row[3],
                    'coverage_level': row[4],
                    'adjustment_suggestion': row[5],
                    'failure_reasons_type': row[6]
                })
            
            return data
            
        except Exception as e:
            logger.error(f"查询设备月度数据失败: {e}")
            return []
        finally:
            if cur:
                cur.close()
            if conn:
                conn.close()
    
    def smart_sample_data(self, data_list, max_samples=100, strategy='time_distributed'):
        """
        智能采样数据，确保样本具有代表性
        
        Args:
            data_list (list): 要采样的数据列表
            max_samples (int): 最大样本数量
            strategy (str): 采样策略
            
        Returns:
            list: 采样后的数据列表
        """
        if len(data_list) <= max_samples:
            return data_list
        
        if strategy == 'time_distributed':
            # 按时间段分布采样
            import random
            sorted_data = sorted(data_list, key=lambda x: x['timestamp'])
            
            # 将数据分成若干个时间段
            num_segments = min(10, max_samples // 10)  # 最多10个时间段
            segment_size = len(sorted_data) // num_segments
            samples_per_segment = max_samples // num_segments
            
            sampled_data = []
            for i in range(num_segments):
                start_idx = i * segment_size
                end_idx = start_idx + segment_size if i < num_segments - 1 else len(sorted_data)
                segment_data = sorted_data[start_idx:end_idx]
                
                if len(segment_data) <= samples_per_segment:
                    sampled_data.extend(segment_data)
                else:
                    sampled_data.extend(random.sample(segment_data, samples_per_segment))
            
            return sorted(sampled_data, key=lambda x: x['timestamp'])
        
        else:  # random
            import random
            return random.sample(data_list, max_samples)
    
    def analyze_monthly_trends(self, data):
        """
        分析月度数据趋势，提供更深入的统计分析
        
        Args:
            data (list): 月度数据列表
            
        Returns:
            dict: 包含各种趋势分析的字典
        """
        if not data:
            return {}
        
        # 按日期分组统计
        daily_stats = defaultdict(lambda: {'normal': 0, 'warning': 0, 'total': 0})
        hourly_stats = defaultdict(lambda: {'normal': 0, 'warning': 0, 'total': 0})
        coverage_level_stats = defaultdict(int)
        failure_type_stats = defaultdict(int)
        
        # 按天和小时统计
        for record in data:
            timestamp = record['timestamp']
            date_key = timestamp.strftime('%Y-%m-%d')
            hour_key = timestamp.hour
            
            daily_stats[date_key]['total'] += 1
            hourly_stats[hour_key]['total'] += 1
            
            if record.get('alarm_status') == 'WARNING':
                daily_stats[date_key]['warning'] += 1
                hourly_stats[hour_key]['warning'] += 1
                
                # 统计故障类型
                for failure_type in record.get('failure_reasons_type', []):
                    failure_type_stats[failure_type] += 1
            else:
                daily_stats[date_key]['normal'] += 1
                hourly_stats[hour_key]['normal'] += 1
            
            # 统计覆盖级别
            coverage_level = record.get('coverage_level', 'UNKNOWN')
            coverage_level_stats[coverage_level] += 1
        
        # 计算每日故障率
        daily_failure_rates = []
        for date, stats in daily_stats.items():
            if stats['total'] > 0:
                failure_rate = (stats['warning'] / stats['total']) * 100
                daily_failure_rates.append(failure_rate)
        
        # 统计分析
        trends_analysis = {
            "日期范围": {
                "开始日期": min(data, key=lambda x: x['timestamp'])['timestamp'].strftime('%Y-%m-%d'),
                "结束日期": max(data, key=lambda x: x['timestamp'])['timestamp'].strftime('%Y-%m-%d'),
                "有效天数": len(daily_stats)
            },
            "故障率趋势": {
                "平均日故障率": f"{statistics.mean(daily_failure_rates):.2f}%" if daily_failure_rates else "0%",
                "最高日故障率": f"{max(daily_failure_rates):.2f}%" if daily_failure_rates else "0%",
                "最低日故障率": f"{min(daily_failure_rates):.2f}%" if daily_failure_rates else "0%",
                "故障率标准差": f"{statistics.stdev(daily_failure_rates):.2f}%" if len(daily_failure_rates) > 1 else "0%"
            },
            "时间分布特征": {
                "故障高发时段": sorted([(hour, stats['warning']) for hour, stats in hourly_stats.items()], 
                                    key=lambda x: x[1], reverse=True)[:3],
                "正常运行高峰": sorted([(hour, stats['normal']) for hour, stats in hourly_stats.items()], 
                                    key=lambda x: x[1], reverse=True)[:3]
            },
            "覆盖级别分布": dict(sorted(coverage_level_stats.items(), key=lambda x: x[1], reverse=True)),
            "主要故障类型": dict(sorted(failure_type_stats.items(), key=lambda x: x[1], reverse=True)[:5]),
            "数据质量": {
                "平均每日记录数": f"{len(data) / len(daily_stats):.1f}" if daily_stats else "0",
                "数据覆盖率": f"{len(daily_stats) / 31 * 100:.1f}%"  # 假设一个月最多31天
            }
        }
        
        return trends_analysis
    
    def calculate_failure_level(self, failure_rate):
        """
        根据故障率计算故障等级和分数 - 使用动态评分算法
        
        Args:
            failure_rate (float): 故障率(百分比)
            
        Returns:
            tuple: (等级名称, 健康度分数, 评分说明)
        """
        # 动态计算健康度分数：健康度 = 100 - 故障率
        health_score = max(0, min(100, 100 - failure_rate))
        
        # 根据健康度分数确定等级
        if health_score >= 98:
            level_name = "优秀级"
        elif health_score >= 90:
            level_name = "良好级"
        elif health_score >= 80:
            level_name = "需关注级"
        elif health_score >= 60:
            level_name = "较差级"
        else:
            level_name = "差级"
        
        score_description = f"健康度{health_score:.1f}分(故障率{failure_rate:.2f}%)"
        
        return level_name, health_score, score_description
    
    def extract_failure_types(self, data):
        """
        从设备数据中提取和统计故障类型
        
        Args:
            data (list): 设备数据列表
            
        Returns:
            tuple: (故障类型统计字典, 故障类型摘要文本)
        """
        if not data:
            return {}, "本月无故障类型数据"
        
        # 统计故障类型
        failure_type_stats = {}
        total_failure_events = 0
        
        for record in data:
            if record.get('alarm_status') == 'WARNING':
                failure_types = record.get('failure_reasons_type', [])
                for failure_type in failure_types:
                    if failure_type:  # 过滤空值
                        failure_type_stats[failure_type] = failure_type_stats.get(failure_type, 0) + 1
                        total_failure_events += 1
        
        # 如果没有故障类型数据
        if not failure_type_stats:
            return {}, "本月无故障类型记录"
        
        # 按发生次数排序，取前5种主要故障类型
        sorted_failure_types = sorted(failure_type_stats.items(), key=lambda x: x[1], reverse=True)[:8]
        main_failure_types = dict(sorted_failure_types)
        
        # 生成故障类型摘要文本
        summary_parts = []
        for failure_type, count in sorted_failure_types:
            percentage = (count / total_failure_events * 100) if total_failure_events > 0 else 0
            summary_parts.append(f"{failure_type}({count}次, {percentage:.1f}%)")
        
        failure_type_summary = f"本月共记录{total_failure_events}次故障事件，主要故障类型：{', '.join(summary_parts[:3])}。"
        
        return main_failure_types, failure_type_summary
    
    def generate_monthly_report(self, camera_id, year, month):
        """
        生成设备月报
        
        Args:
            camera_id (str): 设备ID
            year (int): 年份
            month (int): 月份
            
        Returns:
            tuple: (device_name, normal_count, warning_count, transition_count, 
                    normal_analysis, warning_analysis, transition_analysis, 
                    full_report_text, summary_analysis, main_failure_types, failure_type_summary, analysis_data)
        """
        # 获取设备名称
        device_name = self.data_processor.get_device_name(camera_id)
        
        # 查询月度数据
        data = self.query_device_month(camera_id, year, month)
        
        if not data:
            # 如果没有数据，返回空报告
            empty_report = f"""
                                # {year}年{month}月{device_name}巡检月报

                                ## 报告摘要
                                本月暂无监测数据记录。

                                ## 运行状态分析
                                本月期间未记录到设备运行数据，可能设备处于停机维护状态或数据采集系统暂时中断。

                                ## 建议措施
                                1. 检查设备运行状态
                                2. 确认数据采集系统正常运行
                                3. 联系技术人员进行设备检查

                                ---
                                报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                            """
            summary = "本月暂无监测数据，建议检查设备运行状态和数据采集系统。"
            empty_analysis_data = {
                "设备名称": device_name,
                "监测月份": f"{year}年{month}月",
                "数据统计": {
                    "总记录数": 0,
                    "正常记录数": 0,
                    "告警记录数": 0,
                    "状态转变记录数": 0,
                    "故障率": "0.00%",
                    "故障等级": "无数据",
                    "故障评分": "无数据",
                    "故障详情": "本月无故障类型数据",
                },
                "趋势分析": {},
                "正常运行统计": "本月无正常运行数据",
                "告警情况分析": "本月无告警数据",
                "状态转变分析": "本月无状态转变数据"
            }
            return device_name, 0, 0, 0, "", "", "", empty_report, summary, {}, "本月无故障类型数据", empty_analysis_data
        
        # 预处理数据
        warning_data, non_warning_data, transition_data = self.data_processor.preprocess_device_data(data)
        
        normal_count = len(non_warning_data)
        warning_count = len(warning_data)
        transition_count = len(transition_data)
        
        # 进行月度趋势分析
        trends_analysis = self.analyze_monthly_trends(data)
        
        # 计算故障率
        failure_rate = (warning_count / len(data) * 100) if len(data) > 0 else 0
        failure_level, failure_score, score_description = self.calculate_failure_level(failure_rate)
        
        # 提取故障类型统计
        main_failure_types, failure_type_summary = self.extract_failure_types(data)
        
        # 处理正常数据 - 只进行简单统计
        normal_summary = self.data_processor.summarize_normal_data_simple(non_warning_data)
        # ------------------------------------ 模拟 ------------------------------------ #
        # 处理告警数据
        # warning_analysis = self._analyze_warning_data_monthly(warning_data)
        # logger.info(f"告警数据分析字数: {len(warning_analysis)}")
        # 处理状态转变数据
        # transition_analysis = self._analyze_transition_data_monthly(transition_data)
        # logger.info(f"状态转变数据分析字数: {len(transition_analysis)}")
        warning_analysis = ""
        transition_analysis = ""
        # ------------------------------------ 模拟 ------------------------------------ #
        # 构建分析数据
        analysis_data = {
            "设备名称": device_name,
            "监测月份": f"{year}年{month}月",
            "数据统计": {
                "总记录数": len(data),
                "正常记录数": normal_count,
                "告警记录数": warning_count,
                "状态转变记录数": transition_count,
                "故障率": f"{failure_rate:.2f}%",
                "故障等级": failure_level,
                "故障评分": f"{failure_score}分({score_description})",
                "故障详情": failure_type_summary,
            },
            "趋势分析": trends_analysis,
            "正常运行统计": normal_summary,  # 注：正常数据仅统计，不需AI分析
            # "告警情况分析": warning_analysis,  # 重点：需要AI深度分析
            # "状态转变分析": transition_analysis  # 重点：需要AI深度分析
        }

        # 使用AI生成月报
        try:
            full_report_text = self.llm_analyzer.generate_full_report(analysis_data, 'monthly')
            
            # 生成报告总结
            summary_analysis = self.llm_analyzer._call_llm(
                full_report_text,
                'summary',
                SYSTEM_REPORT_MONTHLY.get("system_prompt_report_monthly_summary")
            )
            
        except Exception as e:
            logger.error(f"AI生成月报失败: {e}")
            # 生成简单的文本报告作为后备
            full_report_text = f"""
                                    # {year}年{month}月{device_name}巡检月报

                                    ## 报告摘要
                                    本月共监测到{len(data)}条记录，其中正常记录{normal_count}条，告警记录{warning_count}条，状态转变记录{transition_count}条。

                                    ## 运行状态分析
                                    - 正常运行比例：{normal_count/len(data)*100:.1f}%
                                    - 告警比例：{warning_count/len(data)*100:.1f}%
                                    - 故障率：{failure_rate:.2f}%
                                    - 故障等级：{failure_level}
                                    - 故障评分：{failure_score}分({score_description})
                                    - 设备整体运行状况：{'良好' if warning_count/len(data) < 0.1 else '需要关注' if warning_count/len(data) < 0.3 else '存在问题'}

                                    ## 建议措施
                                    {"设备运行状况良好，继续保持现有维护策略。" if warning_count/len(data) < 0.1 else "建议加强设备监测和维护。"}

                                    ---
                                    报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                                """
            summary_analysis = f"本月监测{len(data)}条记录，正常运行比例{normal_count/len(data)*100:.1f}%，故障率{failure_rate:.2f}%，故障等级{failure_level}({failure_score}分)，设备状况{'良好' if warning_count/len(data) < 0.1 else '需要关注'}。"
        
        return device_name, normal_count, warning_count, transition_count, normal_summary, warning_analysis, transition_analysis, full_report_text, summary_analysis, main_failure_types, failure_type_summary, analysis_data
    
    def _analyze_warning_data_monthly(self, warning_data):
        """分析月度告警数据"""
        if not warning_data:
            return "本月无告警数据。"
        
        if len(warning_data) > 100:
            # 月报数据量大，使用更大的分块处理
            warning_chunks = self.data_processor.chunk_data(warning_data, 30)  # 每块30条
            chunk_analyses = []
            
            # 限制处理的分组数量，避免过多的API调用
            max_chunks = min(8, len(warning_chunks))  # 最多处理8组，即240条告警数据
            
            for i in range(max_chunks):
                chunk = warning_chunks[i]
                chunk_analysis = self.data_analyzer.analyze_warning_chunk(chunk)
                chunk_analyses.append(f"告警分组 {i+1} (共{len(chunk)}条):\n{chunk_analysis}")
            
            # 如果还有更多数据未处理，添加说明
            if len(warning_chunks) > max_chunks:
                remaining_count = sum(len(chunk) for chunk in warning_chunks[max_chunks:])
                chunk_analyses.append(f"注：还有{remaining_count}条告警数据未详细分析，已包含在统计中。")
            
            # 将所有分组分析结果合并，然后再用LLM进行二次总结
            all_chunk_analyses = "\n\n".join(chunk_analyses)
            return self.llm_analyzer.analyze_warning_data(all_chunk_analyses)
        elif len(warning_data) > 20:
            # 中等数量的告警数据，按原来的逻辑处理
            warning_chunks = self.data_processor.chunk_data(warning_data, 10)
            chunk_analyses = []
            
            for i, chunk in enumerate(warning_chunks):
                chunk_analysis = self.data_analyzer.analyze_warning_chunk(chunk)
                chunk_analyses.append(f"告警组 {i+1}:\n{chunk_analysis}")
            
            all_chunk_analyses = "\n\n".join(chunk_analyses)
            return self.llm_analyzer.analyze_warning_data(all_chunk_analyses)
        else:
            # 数据量不大，直接分析
            warning_summary = self.data_analyzer.analyze_warning_chunk(warning_data)
            return self.llm_analyzer.analyze_warning_data(warning_summary)
    
    def _analyze_transition_data_monthly(self, transition_data):
        """分析月度状态转换数据"""
        if not transition_data:
            return "本月无状态转变数据。"
        
        if len(transition_data) > 60:
            # 月报数据量大，按20条一组分块处理
            transition_chunks = self.data_processor.chunk_data(transition_data, 20)
            chunk_analyses = []
            
            # 限制处理的分组数量
            max_chunks = min(5, len(transition_chunks))  # 最多处理5组，即100条转变数据
            
            for i in range(max_chunks):
                chunk = transition_chunks[i]
                chunk_analysis = self.data_analyzer.analyze_transition_chunk(chunk)
                chunk_analyses.append(f"状态转变分组 {i+1} (共{len(chunk)}条):\n{chunk_analysis}")
            
            # 如果还有更多数据未处理，添加说明
            if len(transition_chunks) > max_chunks:
                remaining_count = sum(len(chunk) for chunk in transition_chunks[max_chunks:])
                chunk_analyses.append(f"注：还有{remaining_count}条状态转变数据未详细分析，已包含在统计中。")
            
            all_chunk_analyses = "\n\n".join(chunk_analyses)
            return self.llm_analyzer.analyze_transition_data(all_chunk_analyses)
        elif len(transition_data) > 10:
            # 中等数量，按原逻辑处理
            transition_chunks = self.data_processor.chunk_data(transition_data, 5)
            chunk_analyses = []
            
            for i, chunk in enumerate(transition_chunks):
                chunk_analysis = self.data_analyzer.analyze_transition_chunk(chunk)
                chunk_analyses.append(f"状态转变组 {i+1}:\n{chunk_analysis}")
            
            all_chunk_analyses = "\n\n".join(chunk_analyses)
            return self.llm_analyzer.analyze_transition_data(all_chunk_analyses)
        else:
            transition_summary = self.data_analyzer.analyze_transition_chunk(transition_data)
            return self.llm_analyzer.analyze_transition_data(transition_summary)
    
    def generate_scene_analysis(self, scene_devices, device_reports, scene_name):
        """
        基于设备月报生成场景级别的分析
        
        Args:
            scene_devices (list): 场景内设备列表
            device_reports (list): 设备月报数据列表
            scene_name (str): 场景名称
            
        Returns:
            tuple: (场景分析, 完整报告, 总结分析, 场景故障类型统计, 场景故障类型摘要)
        """
        try:
            if not device_reports:
                return "本月该场景暂无设备运行数据", "该场景本月无设备月报数据", "场景暂无运行数据", {}, "场景无故障类型数据"
            
            # 聚合统计信息
            total_devices = len(scene_devices)
            total_normal = sum(report['normal_count'] for report in device_reports)
            total_warning = sum(report['warning_count'] for report in device_reports)
            total_records = total_normal + total_warning
            
            # 聚合场景故障类型统计
            scene_failure_types = {}
            for report in device_reports:
                # 从设备月报中获取故障类型信息，如果存在的话
                device_failure_types = report.get('main_failure_types', {})
                if isinstance(device_failure_types, str):
                    try:
                        device_failure_types = json.loads(device_failure_types)
                    except:
                        device_failure_types = {}
                
                # 聚合到场景级别
                for failure_type, count in device_failure_types.items():
                    scene_failure_types[failure_type] = scene_failure_types.get(failure_type, 0) + count
            
            # 生成场景故障类型摘要
            if scene_failure_types:
                sorted_failure_types = sorted(scene_failure_types.items(), key=lambda x: x[1], reverse=True)[:6]
                total_failure_events = sum(scene_failure_types.values())
                scene_failure_summary_parts = []
                for failure_type, count in sorted_failure_types:
                    percentage = (count / total_failure_events * 100) if total_failure_events > 0 else 0
                    scene_failure_summary_parts.append(f"{failure_type}({count}次, {percentage:.1f}%)")
                scene_failure_type_summary = f"场景共记录{total_failure_events}次故障事件，主要故障类型：{', '.join(scene_failure_summary_parts)}。"
            else:
                scene_failure_types = {}
                scene_failure_type_summary = "场景内无故障类型记录"
            
            # 计算百分比
            normal_percentage = (total_normal / total_records * 100) if total_records > 0 else 0
            warning_percentage = (total_warning / total_records * 100) if total_records > 0 else 0
            
            # 计算场景级别的故障率和等级
            failure_rate = warning_percentage
            failure_level, failure_score, score_description = self.calculate_failure_level(failure_rate)
            
            # ---------------------------------- 生成场景分析 ---------------------------------- #
            scene_analysis = f"""
                            {scene_name}场景本月运行状况分析：

                            一、整体运行概况
                            本月{scene_name}场景共监控{total_devices}台设备，累计记录{total_records}条运行数据。
                            - 正常运行记录：{total_normal}条（占比{normal_percentage:.1f}%）
                            - 告警记录：{total_warning}条（占比{warning_percentage:.1f}%）
                            """
            
            # 添加各设备简要分析
            for i, report in enumerate(device_reports, 1):
                device_total = report['normal_count'] + report['warning_count'] + report['transition_count']
                device_normal_pct = (report['normal_count'] / device_total * 100) if device_total > 0 else 0
                scene_analysis += f"{i}. {report['device_name']}：本月共{device_total}条记录，正常率{device_normal_pct:.1f}%\n"
            # ---------------------------------- 生成场景分析 ---------------------------------- #
            # 构建给大模型的场景分析数据
            full_report_prompt_data = {
                "场景名称": scene_name,
                "监测报告月份": f"{datetime.now().year}年{datetime.now().month}月",
                "场景数据总体统计": {
                    "监控设备数量": total_devices,
                    "总记录数": total_records,
                    "正常记录数": total_normal,
                    "告警记录数": total_warning,
                    "故障率": f"{failure_rate:.2f}%",
                    "故障等级": failure_level,
                    "故障评分": f"{failure_score}分({score_description})",
                    "场景故障类型统计": scene_failure_types,
                    "场景故障类型摘要": scene_failure_type_summary
                },
                "场景包含设备类型和分析": [],
                
            }
            
            # 添加各设备的详细分析数据
            for report in device_reports:
                device_info = {
                    "设备名称": report['device_name'],
                    "设备ID": report['camera_id'],
                    "正常记录数": report['normal_count'],
                    "告警记录数": report['warning_count'],
                    "状态转变记录数": report['transition_count'],
                    "正常分析": report.get('normal_analysis', '暂无正常分析')[:200] + "..." if len(report.get('normal_analysis', '')) > 200 else report.get('normal_analysis', '暂无正常分析'),
                    "告警分析": report.get('warning_analysis', '暂无告警分析')[:200] + "..." if len(report.get('warning_analysis', '')) > 200 else report.get('warning_analysis', '暂无告警分析'),
                    "状态转变分析": report.get('transition_analysis', '暂无转变分析')[:200] + "..." if len(report.get('transition_analysis', '')) > 200 else report.get('transition_analysis', '暂无转变分析'),
                    "设备总结": report.get('summary_analysis', '暂无总结分析')[:100] + "..." if len(report.get('summary_analysis', '')) > 100 else report.get('summary_analysis', '暂无总结分析'),
                    "故障类型摘要": report.get('failure_type_summary', '暂无故障类型摘要')
                }
                full_report_prompt_data["场景包含设备类型和分析"].append(device_info)
            
            # 使用大模型生成场景完整报告
            try:
                full_report = self.llm_analyzer._call_llm(
                    str(full_report_prompt_data),
                    'scene',
                    SYSTEM_REPORT_MONTHLY.get("system_prompt_report_monthly_scene")
                )
                
                # 生成场景报告总结
                summary_analysis = self.llm_analyzer._call_llm(
                    full_report,
                    'scene_summary',
                    SYSTEM_REPORT_MONTHLY.get("system_prompt_report_monthly_scene_summary")
                )
                
            except Exception as e:
                logger.error(f"AI生成场景报告失败: {e}")
                # 生成简单的文本报告作为后备
                full_report = f"""
                                # {scene_name}场景月度巡检报告

                                ## 报告摘要
                                本月{scene_name}场景共监控{total_devices}台设备，累计记录{total_records}条数据，其中正常记录{total_normal}条，告警记录{total_warning}条。

                                ## 运行状态分析
                                - 正常运行比例：{normal_percentage:.1f}%
                                - 告警比例：{warning_percentage:.1f}%
                                - 故障率：{failure_rate:.2f}%
                                - 故障等级：{failure_level}
                                - 故障评分：{failure_score}分({score_description})

                                ## 设备运行概况
                                """
                
                for report in device_reports:
                    device_total = report['normal_count'] + report['warning_count'] + report['transition_count']
                    device_failure_rate = (report['warning_count'] / device_total * 100) if device_total > 0 else 0
                    full_report += f"""
                                - {report['device_name']}（ID: {report['camera_id']}）：记录{device_total}条，故障率{device_failure_rate:.1f}%
                                """
                
                full_report += f"""
                                ## 建议措施
                                {'场景整体运行状况良好，继续保持现有维护策略。' if warning_percentage < 15 else '建议加强场景内设备监测和维护。'}

                                ---
                                报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                                """
                
                summary_analysis = f"{scene_name}场景本月监控{total_devices}台设备，累计记录{total_records}条数据，正常率{normal_percentage:.1f}%，故障率{failure_rate:.2f}%，故障等级{failure_level}({failure_score}分)，场景状况{'良好' if warning_percentage < 15 else '需要关注'}。"
            
            return scene_analysis, full_report, summary_analysis, scene_failure_types, scene_failure_type_summary
            
        except Exception as e:
            logger.error(f"生成场景分析失败: {e}")
            return "场景分析生成失败", "场景报告生成失败", "场景总结生成失败", {}, "场景故障类型分析失败"

# -------------------------------------------------------- #
#                     兼容性函数（保持旧接口）                    #
# -------------------------------------------------------- #

# 为了保持向后兼容，提供旧的函数接口
def generate_device_report(camera_id, reference_date=None):
    """兼容性函数 - 生成设备报告"""
    daily_generator = DailyReportGenerator()
    return daily_generator.generate_device_report(camera_id, reference_date)

def generate_daily_report(camera_id, reference_date=None):
    """兼容性函数 - 生成每日报告"""
    daily_generator = DailyReportGenerator()
    return daily_generator.generate_daily_report(camera_id, reference_date)

def generate_monthly_report(camera_id, year, month):
    """兼容性函数 - 生成月报"""
    monthly_generator = MonthlyReportGenerator()
    return monthly_generator.generate_monthly_report(camera_id, year, month)

def generate_scene_analysis(scene_devices, device_reports, scene_name):
    """兼容性函数 - 生成场景分析"""
    monthly_generator = MonthlyReportGenerator()
    return monthly_generator.generate_scene_analysis(scene_devices, device_reports, scene_name)


if __name__ == "__main__":
    # 使用当前日期作为参考日期
    current_date = datetime.now().strftime('%Y-%m-%d')
    logger.info(f"使用参考日期: {current_date}")
    
    # 生成报告
    camera_id = "4041"
    
    # 测试日报生成
    # logger.info("测试日报生成:")
    # daily_generator = DailyReportGenerator()
    # daily_result = daily_generator.generate_daily_report(camera_id, current_date)
    # logger.info(f"日报生成结果: {daily_result[0]} - 正常:{daily_result[1]}, 告警:{daily_result[2]}, 转变:{daily_result[3]}")
    
    # 测试月报生成
    logger.info("=" * 50)
    logger.info("测试月报生成:")
    current_year = datetime.now().year
    current_month = datetime.now().month
    monthly_generator = MonthlyReportGenerator()
    monthly_result = monthly_generator.generate_monthly_report(camera_id, current_year, current_month)
    logger.info(f"月报生成结果: {monthly_result[0]} - 正常:{monthly_result[1]}, 告警:{monthly_result[2]}, 转变:{monthly_result[3]}")
    logger.info(f"总结: {monthly_result[8]}")
