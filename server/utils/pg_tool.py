from pathlib import Path

import psycopg2
import yaml
import sys
import os
import logging
# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config_file import config as config_pg
from server.utils.logger import setup_logging
sys.path.append(os.getcwd())  # 添加当前工作目录到系统路径

# 配置日志
setup_logging()
logger = logging.getLogger(__name__)
# def load_config():
#     """加载配置文件

#     从项目根目录的configs/env.yaml文件中加载数据库配置信息

#     Returns:
#         dict: 包含数据库配置信息的字典
#         None: 加载失败时返回None

#     Raises:
#         Exception: 配置文件读取异常
#     """
#     try:
#         config_path = Path(__file__).parent.parent.parent / "configs" / "env.yaml"
#         with open(config_path, 'r', encoding='utf-8') as f:
#             return yaml.safe_load(f)
#     except Exception as e:
#         print(f"加载配置文件失败: {e}")
#         return None


def connect_db():
    """创建数据库连接

    从配置文件获取数据库连接信息并创建连接

    Returns:
        Connection: PostgreSQL数据库连接对象
        None: 连接失败时返回None

    Raises:
        Exception: 数据库连接异常
    """
    try:
        # config = load_config()
        config = config_pg.env
        if not config:
            raise Exception("无法加载配置文件")

        db_config = config['database']['postgres']

        conn = psycopg2.connect(
            dbname=db_config['dbname'],
            user=db_config['user'],
            password=db_config['password'],
            host=db_config['host'],
            port=db_config['port']
        )
        return conn
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None


def query_data(table, conditions=None, fields=None, order_by=None, limit=None):
    """执行数据库查询

    Args:
        table (str): 表名
        conditions (dict): 查询条件
        fields (list): 查询字段
        order_by (str): 排序条件
        limit (int): 限制返回记录数

    Returns:
        list: 查询结果列表

    Raises:
        Exception: 数据库查询异常
    """
    conn = connect_db()
    if not conn:
        return []
    try:
        cur = conn.cursor()
        # 构建查询字段
        if fields:
            fields_str = ', '.join(fields)
        else:
            fields_str = '*'
        # 构建查询条件
        query_sql = f"SELECT {fields_str} FROM {table}"
        condition_values = []
        if conditions:
            condition_str = ' AND '.join([f"{key} = %s" for key in conditions.keys()])
            condition_values = list(conditions.values())
            query_sql += f" WHERE {condition_str}"
        # 添加排序
        if order_by:
            query_sql += f" ORDER BY {order_by}"
        # 添加结果数量限制
        if limit:
            query_sql += f" LIMIT {limit}"
        cur.execute(query_sql, condition_values)
        # 获取查询结果
        result = cur.fetchall()
        return result
    except Exception as e:
        logger.error(f"查询数据错误: {e}")
        return []
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()

def query_morning_data(camera_id, time_period="morning"):
    """查询指定摄像头今天和昨天指定时间段的数据，每天只返回最接近目标时间点的一条记录

    Args:
        camera_id (str): 摄像头ID
        time_period (str): 时间段，"morning"表示早上7-9点，"afternoon"表示下午4-6点

    Returns:
        dict: 以日期为键的字典，值为该日期最接近目标时间点的完整数据记录
    """
    conn = connect_db()
    if not conn:
        return {}
    
    # 根据time_period确定查询的时间段和目标时间点
    if time_period == "morning":
        target_hour = 7  # 目标时间点为早上7点
        hour_range_start = 7
        hour_range_end = 9
    else:  # afternoon
        target_hour = 16  # 目标时间点为下午4点
        hour_range_start = 16
        hour_range_end = 18
    
    try:
        cur = conn.cursor()
        
        query_sql = """
            WITH ranked_records AS (
                SELECT 
                    camera_id, video_id, frame_number, timestamp, frame_path,
                    coverage_rate, coverage_level, alarm_status, analysis_detail,
                    is_abnormal, do_value, mlss_value, adjustment_suggestion,
                    failure_reasons_type, failure_reasons_number,
                    DATE(timestamp) as record_date,
                    ABS(EXTRACT(HOUR FROM timestamp) - %s) + 
                    EXTRACT(MINUTE FROM timestamp)::float / 60 as time_diff,
                    ROW_NUMBER() OVER (
                        PARTITION BY DATE(timestamp) 
                        ORDER BY ABS(EXTRACT(HOUR FROM timestamp) - %s) + 
                                EXTRACT(MINUTE FROM timestamp)::float / 60
                    ) as rn
                FROM frame_analysis 
                WHERE camera_id = %s 
                AND EXTRACT(HOUR FROM timestamp) BETWEEN %s AND %s
                AND DATE(timestamp) >= CURRENT_DATE - INTERVAL '1 day'
                AND DATE(timestamp) <= CURRENT_DATE
            )
            SELECT *
            FROM ranked_records
            WHERE rn = 1
            ORDER BY timestamp
        """
        
        cur.execute(query_sql, [target_hour, target_hour, camera_id, hour_range_start, hour_range_end])
        results = cur.fetchall()
        
        # 将结果组织成以日期为键的字典
        data_result = {}
        fields = [
            "camera_id", "video_id", "frame_number", "timestamp", "frame_path",
            "coverage_rate", "coverage_level", "alarm_status", "analysis_detail",
            "is_abnormal", "do_value", "mlss_value", "adjustment_suggestion",
            "failure_reasons_type", "failure_reasons_number", "record_date",
            "time_diff", "rn"
        ]
        
        for row in results:
            # 获取日期作为键
            date_key = row[3].date().isoformat()  # timestamp字段的索引是3
            
            # 将行数据转换为字典
            row_dict = dict(zip(fields, row))
            
            # 只保留需要的字段
            filtered_dict = {
                "camera_id": row_dict["camera_id"],
                "video_id": row_dict["video_id"],
                "frame_number": row_dict["frame_number"],
                "timestamp": row_dict["timestamp"],
                "frame_path": row_dict["frame_path"],
                "coverage_rate": row_dict["coverage_rate"],
                "coverage_level": row_dict["coverage_level"],
                "alarm_status": row_dict["alarm_status"],
                "analysis_detail": row_dict["analysis_detail"],
                "is_abnormal": row_dict["is_abnormal"],
                "do_value": row_dict["do_value"],
                "mlss_value": row_dict["mlss_value"],
                "adjustment_suggestion": row_dict["adjustment_suggestion"],
                "failure_reasons_type": row_dict["failure_reasons_type"],
                "failure_reasons_number": row_dict["failure_reasons_number"]
            }
            
            # 将数据添加到对应日期
            data_result[date_key] = filtered_dict
        
        return data_result
        
    except Exception as e:
        period_name = "早上" if time_period == "morning" else "下午"
        logger.error(f"查询{period_name}数据错误: {e}")
        return {}
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()

# 图像元数据相关操作函数
def insert_image_metadata(filepath, capture_timestamp, robot_state_at_capture=None):
    """插入图像元数据记录
    
    Args:
        filepath (str): 图像文件路径
        capture_timestamp (datetime): 图像捕获时间戳
        robot_state_at_capture (str, optional): 捕获时机器人状态
        
    Returns:
        int: 插入记录的ID，失败返回None
        
    Raises:
        Exception: 数据库操作异常
    """
    conn = connect_db()
    if not conn:
        return None
    try:
        cur = conn.cursor()
        
        # 构建插入SQL
        insert_sql = """
            INSERT INTO image_metadata (filepath, capture_timestamp, robot_state_at_capture)
            VALUES (%s, %s, %s)
            RETURNING id
        """
        
        # 执行插入操作
        cur.execute(insert_sql, [filepath, capture_timestamp, robot_state_at_capture])
        
        # 获取插入记录的ID
        result = cur.fetchone()
        conn.commit()
        
        return result[0] if result else None
        
    except Exception as e:
        conn.rollback()
        logger.error(f"插入图像元数据失败: {e}")
        return None
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()

def get_image_metadata(conditions=None, fields=None, order_by="capture_timestamp DESC", limit=None):
    """查询图像元数据
    
    Args:
        conditions (dict, optional): 查询条件
        fields (list, optional): 查询字段
        order_by (str, optional): 排序条件，默认按捕获时间降序
        limit (int, optional): 限制返回记录数
        
    Returns:
        list: 查询结果列表
        
    Raises:
        Exception: 数据库查询异常
    """
    return query_data("image_metadata", conditions, fields, order_by, limit)

def update_image_metadata(image_id, update_data):
    """更新图像元数据
    
    Args:
        image_id (int): 图像记录ID
        update_data (dict): 要更新的字段和值
        
    Returns:
        bool: 更新是否成功
        
    Raises:
        Exception: 数据库操作异常
    """
    conn = connect_db()
    if not conn:
        return False
    try:
        cur = conn.cursor()
        
        # 构建更新SQL
        set_clause = ", ".join([f"{key} = %s" for key in update_data.keys()])
        values = list(update_data.values()) + [image_id]
        
        update_sql = f"""
            UPDATE image_metadata
            SET {set_clause}
            WHERE id = %s
        """
        
        # 执行更新操作
        cur.execute(update_sql, values)
        conn.commit()
        
        return cur.rowcount > 0
        
    except Exception as e:
        conn.rollback()
        logger.error(f"更新图像元数据失败: {e}")
        return False
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()

def delete_image_metadata(image_id):
    """删除图像元数据记录
    
    Args:
        image_id (int): 图像记录ID
        
    Returns:
        bool: 删除是否成功
        
    Raises:
        Exception: 数据库操作异常
    """
    conn = connect_db()
    if not conn:
        return False
    try:
        cur = conn.cursor()
        
        # 构建删除SQL
        delete_sql = """
            DELETE FROM image_metadata
            WHERE id = %s
        """
        
        # 执行删除操作
        cur.execute(delete_sql, [image_id])
        conn.commit()
        
        return cur.rowcount > 0
        
    except Exception as e:
        conn.rollback()
        logger.error(f"删除图像元数据失败: {e}")
        return False
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()

def get_image_metadata_by_filepath(filepath):
    """通过文件路径查询图像元数据
    
    Args:
        filepath (str): 图像文件路径
        
    Returns:
        dict: 图像元数据记录，失败返回None
        
    Raises:
        Exception: 数据库查询异常
    """
    conn = connect_db()
    if not conn:
        return None
    try:
        cur = conn.cursor()
        
        # 构建查询SQL
        query_sql = """
            SELECT id, filepath, capture_timestamp, robot_state_at_capture, created_at
            FROM image_metadata
            WHERE filepath = %s
        """
        
        # 执行查询操作
        cur.execute(query_sql, [filepath])
        
        # 获取查询结果
        result = cur.fetchone()
        
        if result:
            return {
                "id": result[0],
                "filepath": result[1],
                "capture_timestamp": result[2],
                "robot_state_at_capture": result[3],
                "created_at": result[4]
            }
        return None
        
    except Exception as e:
        logger.error(f"查询图像元数据失败: {e}")
        return None
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()

def query_device_history(camera_id):
    """根据设备ID查询历史数据
    
    查询从今天0点到当前时刻的历史数据
    
    Args:
        camera_id (str): 设备ID/摄像头ID
        
    Returns:
        list: 包含查询结果的列表，每个元素为一条记录的字典
        
    Raises:
        Exception: 数据库查询异常
    """
    conn = connect_db()
    if not conn:
        return []
    
    try:
        cur = conn.cursor()
        
        query_sql = """
            SELECT 
                video_id, timestamp, coverage_level, analysis_detail, adjustment_suggestion
            FROM 
                frame_analysis
            WHERE 
                camera_id = %s
                AND timestamp >= DATE_TRUNC('day', CURRENT_DATE)
                AND timestamp <= CURRENT_TIMESTAMP
            ORDER BY 
                timestamp DESC
        """
        
        logger.debug(f"执行查询，设备ID: {camera_id}, 查询时间范围: 从今天0点到现在")
        cur.execute(query_sql, [camera_id])
        results = cur.fetchall()

        # 记录查询结果数量
        logger.debug(f"查询到 {len(results)} 条原始记录")

        # 如果没有结果，尝试直接查询设备ID是否存在
        if len(results) == 0:
            check_sql = """
                SELECT COUNT(*) FROM frame_analysis WHERE camera_id = %s
            """
            cur.execute(check_sql, [camera_id])
            count_result = cur.fetchone()
            logger.debug(f"设备 {camera_id} 在数据库中共有 {count_result[0]} 条记录")
        
        # 将结果格式化为字典列表
        formatted_results = []
        fields = ["video_id", "timestamp", "coverage_level", "analysis_detail", "adjustment_suggestion"]
        
        for row in results:
            row_dict = dict(zip(fields, row))
            formatted_results.append(row_dict)
        
        return formatted_results
        
    except Exception as e:
        logger.error(f"查询设备历史数据失败: {e}")
        return []
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()

# def query_device_history_by_date(camera_id, start_date=None, end_date=None):
#     """根据设备ID和日期范围查询历史数据
    
#     Args:
#         camera_id (str): 设备ID/摄像头ID
#         start_date (str, optional): 开始日期，格式为'YYYY-MM-DD'，默认为昨天
#         end_date (str, optional): 结束日期，格式为'YYYY-MM-DD'，默认为今天
        
#     Returns:
#         list: 包含查询结果的列表，每个元素为一条记录的字典
        
#     Raises:
#         Exception: 数据库查询异常
#     """
#     conn = connect_db()
#     if not conn:
#         return []
    
#     try:
#         cur = conn.cursor()
        
#         # 构建查询SQL
#         query_sql = """
#             SELECT 
#                 video_id, timestamp, coverage_level, analysis_detail, adjustment_suggestion
#             FROM 
#                 frame_analysis
#             WHERE 
#                 camera_id = %s
#         """
        
#         parameters = [camera_id]
        
#         # 添加日期条件
#         if start_date:
#             query_sql += " AND timestamp::date >= %s"
#             parameters.append(start_date)
#         else:
#             query_sql += " AND timestamp >= CURRENT_DATE - INTERVAL '1 day'"
            
#         if end_date:
#             query_sql += " AND timestamp::date <= %s"
#             parameters.append(end_date)
#         else:
#             query_sql += " AND timestamp <= CURRENT_TIMESTAMP"
        
#         # 添加排序
#         query_sql += " ORDER BY timestamp DESC"
        
#         print(f"执行查询，设备ID: {camera_id}, 查询时间范围: 从{start_date if start_date else '昨天'}到{end_date if end_date else '今天'}")
#         cur.execute(query_sql, parameters)
#         results = cur.fetchall()
        
#         # 打印查询结果数量
#         print(f"查询到 {len(results)} 条原始记录")
        
#         # 如果没有结果，尝试直接查询设备ID是否存在
#         if len(results) == 0:
#             check_sql = """
#                 SELECT COUNT(*) FROM frame_analysis WHERE camera_id = %s
#             """
#             cur.execute(check_sql, [camera_id])
#             count_result = cur.fetchone()
#             print(f"设备 {camera_id} 在数据库中共有 {count_result[0]} 条记录")
            
#             if count_result[0] > 0:
#                 # 查询最早和最晚的记录时间
#                 time_sql = """
#                     SELECT 
#                         MIN(timestamp) as earliest, 
#                         MAX(timestamp) as latest 
#                     FROM frame_analysis 
#                     WHERE camera_id = %s
#                 """
#                 cur.execute(time_sql, [camera_id])
#                 time_result = cur.fetchone()
#                 if time_result:
#                     print(f"设备 {camera_id} 的记录时间范围: 从 {time_result[0]} 到 {time_result[1]}")
        
#         # 将结果格式化为字典列表
#         formatted_results = []
#         fields = ["video_id", "timestamp", "coverage_level", "analysis_detail", "adjustment_suggestion"]
        
#         for row in results:
#             row_dict = dict(zip(fields, row))
#             formatted_results.append(row_dict)
        
#         return formatted_results
        
#     except Exception as e:
#         print(f"查询设备历史数据失败: {e}")
#         return []
#     finally:
#         if cur:
#             cur.close()
#         if conn:
#             conn.close()

def query_device_today(camera_id, reference_date=None):
    """查询设备从今天0点到当前时刻的历史数据
    
    Args:
        camera_id (str): 设备ID/摄像头ID
        reference_date (str, optional): 参考日期，格式为'YYYY-MM-DD'，默认为当前日期
        
    Returns:
        list: 包含查询结果的列表，每个元素为一条记录的字典
        
    Raises:
        Exception: 数据库查询异常
    """
    conn = connect_db()
    if not conn:
        return []
    
    try:
        cur = conn.cursor()
        
        # 构建查询SQL
        if reference_date:
            # 如果提供了参考日期，查询从参考日期0点到参考日期23:59:59的数据
            query_sql = """
                SELECT 
                    video_id, timestamp, coverage_level, analysis_detail, adjustment_suggestion,
                    alarm_status, failure_reasons_type
                FROM 
                    frame_analysis
                WHERE 
                    camera_id = %s
                    AND timestamp >= %s::date
                    AND timestamp < %s::date + INTERVAL '1 day'
                ORDER BY 
                    timestamp DESC
            """
            parameters = [camera_id, reference_date, reference_date]
            logger.debug(f"执行查询，设备ID: {camera_id}, 查询时间范围: 从{reference_date} 00:00:00到{reference_date} 23:59:59")
        else:
            # 如果没有提供参考日期，查询从今天0点到当前时刻的数据
            query_sql = """
                SELECT 
                    video_id, timestamp, coverage_level, analysis_detail, adjustment_suggestion,
                    alarm_status, failure_reasons_type
                FROM 
                    frame_analysis
                WHERE 
                    camera_id = %s
                    AND timestamp >= CURRENT_DATE
                    AND timestamp <= CURRENT_TIMESTAMP
                ORDER BY 
                    timestamp DESC
            """
            parameters = [camera_id]
            logger.debug(f"执行查询，设备ID: {camera_id}, 查询时间范围: 从今天0点到当前时刻")

        cur.execute(query_sql, parameters)
        results = cur.fetchall()

        # 记录查询结果数量
        logger.debug(f"查询到 {len(results)} 条原始记录")

        # 如果没有结果，尝试直接查询设备ID是否存在
        if len(results) == 0:
            check_sql = """
                SELECT COUNT(*) FROM frame_analysis WHERE camera_id = %s
            """
            cur.execute(check_sql, [camera_id])
            count_result = cur.fetchone()
            logger.debug(f"设备 {camera_id} 在数据库中共有 {count_result[0]} 条记录")
        
        # 将结果格式化为字典列表
        formatted_results = []
        fields = ["video_id", "timestamp", "coverage_level", "analysis_detail", "adjustment_suggestion", 
                 "alarm_status", "failure_reasons_type"]
        
        for row in results:
            row_dict = dict(zip(fields, row))
            formatted_results.append(row_dict)
        
        return formatted_results
        
    except Exception as e:
        logger.error(f"查询设备今日数据失败: {e}")
        return []
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    # 查询早上7点的数据
    # morning_data = query_morning_data('4047', "morning")
    # print("早上7点数据:")
    # for date, data in morning_data.items():
    #     print(f"\n日期: {date}")
    #     for field, value in data.items():
    #         print(f"{field}: {value}")
    
    # # 查询下午4点的数据
    # afternoon_data = query_morning_data('4047', "afternoon")
    # print("\n下午4点数据:")
    # for date, data in afternoon_data.items():
    #     print(f"\n日期: {date}")
    #     for field, value in data.items():
    #         print(f"{field}: {value}")
    
    # print("\n" + "="*50)
    # print("测试图像元数据相关功能")
    # print("="*50)
    
    # # 导入所需模块
    # from datetime import datetime
    
    # # 1. 测试插入图像元数据
    # print("\n1. 测试插入图像元数据")
    # test_filepath = "assets/frame_1.jpg"
    # test_timestamp = datetime.now()
    # test_robot_state = "正常运行"
    
    # print("\n测试完成")

    # # 测试设备历史数据查询功能
    # print("\n" + "="*50)
    # print("测试设备历史数据查询功能")
    # print("="*50)

    # # 测试查询设备ID为'4052'的历史数据（默认查询昨天到今天）
    device_id = '4052'
    # history_data = query_device_history(device_id)

    # print(f"\n设备 {device_id} 的历史数据查询结果(昨天到今天):")
    # print(f"共查询到 {len(history_data)} 条记录")

    # # 显示前5条记录的详细信息（如果有的话）
    # display_count = min(5, len(history_data))
    # if display_count > 0:
    #     print(f"\n显示前 {display_count} 条记录:")
    #     for i, record in enumerate(history_data[:display_count]):
    #         print(f"\n记录 {i+1}:")
    #         print(f"视频ID: {record['video_id']}")
    #         print(f"时间戳: {record['timestamp']}")
    #         print(f"覆盖等级: {record['coverage_level']}")
    #         print(f"分析详情: {record['analysis_detail']}")
    #         print(f"调整建议: {record['adjustment_suggestion']}")
    # else:
    #     print("未查询到历史数据记录")
    
    # # 测试使用指定日期范围查询
    # print("\n" + "="*50)
    # print("测试使用指定日期范围查询设备历史数据")
    # print("="*50)
    
    # # 查询2025年6月3日的数据
    # future_data = query_device_history_by_date(device_id, 
    #                                           start_date='2025-06-03', 
    #                                           end_date='2025-06-03')
    
    # print(f"\n设备 {device_id} 在2025-06-03的历史数据查询结果:")
    # print(f"共查询到 {len(future_data)} 条记录")
    
    # # 显示前5条记录的详细信息（如果有的话）
    # display_count = min(5, len(future_data))
    # if display_count > 0:
    #     print(f"\n显示前 {display_count} 条记录:")
    #     for i, record in enumerate(future_data[:display_count]):
    #         print(f"\n记录 {i+1}:")
    #         print(f"视频ID: {record['video_id']}")
    #         print(f"时间戳: {record['timestamp']}")
    #         print(f"覆盖等级: {record['coverage_level']}")
    #         print(f"分析详情: {record['analysis_detail']}")
    #         print(f"调整建议: {record['adjustment_suggestion']}")
    # else:
    #     print("未查询到历史数据记录")

    # # 测试查询今天数据功能
    # print("\n" + "="*50)
    # print("测试查询设备今日数据功能")
    # print("="*50)
    
    # # 查询今天的数据
    # today_data = query_device_today(device_id)
    
    # print(f"\n设备 {device_id} 的今日数据查询结果:")
    # print(f"共查询到 {len(today_data)} 条记录")
    
    # 参考日期测试 - 使用2025-06-03作为参考日期
    logger.info("=" * 50)
    logger.info("测试使用参考日期查询设备当日数据")
    logger.info("=" * 50)

    reference_date = '2025-06-03'
    reference_day_data = query_device_today(device_id, reference_date)

    logger.info(f"设备 {device_id} 在 {reference_date} 的当日数据查询结果:")
    logger.info(f"共查询到 {len(reference_day_data)} 条记录")

    # 显示前5条记录的详细信息（如果有的话）
    display_count = min(5, len(reference_day_data))
    if display_count > 0:
        logger.info(f"显示前 {display_count} 条记录:")
        for i, record in enumerate(reference_day_data[:display_count]):
            logger.info(f"记录 {i+1}:")
            logger.info(f"视频ID: {record['video_id']}")
            logger.info(f"时间戳: {record['timestamp']}")
            logger.info(f"覆盖等级: {record['coverage_level']}")
            logger.info(f"分析详情: {record['analysis_detail']}")
            logger.info(f"调整建议: {record['adjustment_suggestion']}")
            logger.info(f"告警状态: {record['alarm_status']}")
            logger.info(f"故障原因类型: {record['failure_reasons_type']}")
    else:
        logger.warning("未查询到历史数据记录")