import logging
import cv2
from pathlib import Path
from typing import Dict, List, Tuple, Any


class BaseFrameHandler:
    """视频帧处理器基类
    
    所有具体的帧处理器都应该继承此类并实现process_frame方法
    
    Attributes:
        processor (object): 主处理器实例的引用
    """
    
    def __init__(self, processor):
        """初始化处理器
        
        Args:
            processor (VideoFrameProcessor): 主处理器实例
        """
        self.processor = processor
        logging.info(f"初始化 {self.__class__.__name__}")
    
    def process_frame(self, frame, frame_count, save_dir, camera_id, sensor_data,
                      threshold, system_type, standard_image_path=None, current_time=None) -> Tuple:
        """处理单个视频帧并返回分析结果
        
        Args:
            frame: 视频帧图像数据
            frame_count: 帧计数
            save_dir: 保存目录
            camera_id: 摄像头ID
            sensor_data: 传感器数据
            threshold: 覆盖率阈值
            system_type: 系统类型
            standard_image_path: 标准图片路径
            current_time: 当前时间
            
        Returns:
            tuple: (覆盖率, 分析结果, 警报状态, 是否异常, 图片路径, 分析建议, 故障类型列表)
        """
        raise NotImplementedError("子类必须实现process_frame方法")
    
    def _prepare_frame(self, frame, save_dir, camera_id, current_time, resize_to=(1600, 900)):
        """准备视频帧，包括调整尺寸和保存
        
        Args:
            frame: 视频帧图像数据
            save_dir: 保存目录
            camera_id: 摄像头ID
            current_time: 当前时间
            resize_to: 调整尺寸的目标大小，默认为(1600, 900)
            
        Returns:
            tuple: (调整后的帧, 帧保存路径)
        """
        # 创建文件名
        frame_filename = f"frame_{camera_id}_{current_time.strftime('%Y_%m_%d_%H_%M_%S')}.jpg"
        frame_path = save_dir / frame_filename
        
        # 调整图像尺寸
        resized_frame = cv2.resize(frame, resize_to, interpolation=cv2.INTER_AREA)
        
        # 保存调整后的帧
        cv2.imwrite(str(frame_path), resized_frame)
        
        return resized_frame, frame_path
    
    def _prepare_frame_without_save(self, frame, save_dir, camera_id, current_time, resize_to=(1600, 900)):
        """准备视频帧，只调整尺寸但不保存
        
        Args:
            frame: 视频帧图像数据
            save_dir: 保存目录
            camera_id: 摄像头ID
            current_time: 当前时间
            resize_to: 调整尺寸的目标大小，默认为(1600, 900)
            
        Returns:
            tuple: (调整后的帧, 帧保存路径)
        """
        # 创建文件名但不保存
        frame_filename = f"frame_{camera_id}_{current_time.strftime('%Y_%m_%d_%H_%M_%S')}.jpg"
        frame_path = save_dir / frame_filename
        
        # 调整图像尺寸
        resized_frame = cv2.resize(frame, resize_to, interpolation=cv2.INTER_AREA)
        
        # 不保存图片，只返回处理后的帧和路径
        return resized_frame, frame_path
    
    def _save_frame(self, frame, frame_path):
        """保存帧图像到指定路径
        
        Args:
            frame: 图像数据
            frame_path: 保存路径
            
        Returns:
            bool: 保存是否成功
        """
        try:
            cv2.imwrite(str(frame_path), frame)
            return True
        except Exception as e:
            logging.error(f"保存图片失败: {str(e)}")
            return False
    
    def _process_image_comparison(self, frame, frame_path, standard_image_path=None, system_type=None):
        """处理图像比较
        
        调用主处理器的_process_image_comparison方法
        
        Args:
            frame: 当前帧图像
            frame_path: 帧图像保存路径
            standard_image_path: 标准图像路径
            system_type: 系统类型
            
        Returns:
            dict: 图像分析结果
        """
        return self.processor._process_image_comparison(
            frame, frame_path, standard_image_path, system_type
        )
    
    def _determine_alarm_status(self, coverage_float: float, threshold: float) -> tuple:
        """确定警报状态
        
        Args:
            coverage_float (float): 覆盖率
            threshold (float): 阈值
            
        Returns:
            tuple: (警报状态, 是否异常)
        """
        return self.processor._determine_alarm_status(coverage_float, threshold) 