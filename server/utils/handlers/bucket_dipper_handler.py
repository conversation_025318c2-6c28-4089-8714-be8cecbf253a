import logging
import time
import cv2
from pathlib import Path
from typing import Dict, List, Tuple, Any

from server.utils.handlers.base_handler import BaseFrameHandler
# 移除未使用的导入：YoloRegionCounter
# from llms.yolo_region import YoloRegionCounter  # 未使用，已移除


class BucketDipperHandler(BaseFrameHandler):
    """耙斗处理器类
    
    负责处理耙斗设备相关的视频帧，包括倾斜检测和卡住检测
    
    Attributes:
        processor: 主处理器实例，必须包含以下属性：
            - device_incline_state (dict): 设备倾斜状态字典，格式为 {device_id: {"alarm_triggered": bool, "last_status": str}}
            - region_state (dict): 区域状态字典，格式为 {region_name: {"last_state": int/None, "timer": int, "alarm_triggered": bool}}
            - incline_reset_enabled (bool): 是否启用倾斜检测报警重置
            - incline_reset_threshold (int): 倾斜检测报警重置阈值
            - incline_reset_counter (int): 倾斜检测报警重置计数器
            - stuck_reset_enabled (bool): 是否启用卡住检测报警重置
            - stuck_reset_threshold (int): 卡住检测报警重置阈值
            - stuck_reset_counter (int): 卡住检测报警重置计数器
            - reset_enabled (bool): 是否启用重置机制
            - reset_threshold (int): 重置阈值
            - stuck_threshold (int): 卡住判断阈值
        device_positions (dict): 设备位置描述映射
        region_descriptions (dict): 区域描述映射
    """
    
    def __init__(self, processor):
        """初始化处理器
        
        Args:
            processor (VideoFrameProcessor): 主处理器实例
        """
        super().__init__(processor)
        
        # 设备位置描述映射
        self.device_positions = {
            1: "左边",
            2: "中间",
            3: "右边"
        }
        
        # 区域描述映射
        self.region_descriptions = {
            "region-01": "区域1(左边区域)",
            "region-02": "区域2(中间区域)",
            "region-03": "区域3(右边区域)"
        }
        # --- 新增的配置验证逻辑 ---
        self._validate_configuration()

    def _validate_configuration(self):
        """验证处理器状态中的ID和名称是否在描述映射中都有定义"""
        for device_id in self.processor.device_incline_state:
            if device_id not in self.device_positions:
                logging.warning(
                    f"配置不匹配：在 'processor.device_incline_state' 中找到的 device_id '{device_id}' "
                    f"在 'self.device_positions' 中没有对应的描述。"
                )

        for region_name in self.processor.region_state:
            if region_name not in self.region_descriptions:
                logging.warning(
                    f"配置不匹配：在 'processor.region_state' 中找到的 region_name '{region_name}' "
                    f"在 'self.region_descriptions' 中没有对应的描述。"
                )
        logging.info("配置验证完成，所有设备和区域都有对应的描述。")
    def process_frame(self, frame, frame_count, save_dir, camera_id, sensor_data,
                      threshold, system_type, standard_image_path=None, current_time=None) -> Tuple:
        """处理耙斗相关的视频帧
        
        Args:
            frame: 视频帧图像数据
            frame_count: 帧计数
            save_dir: 保存目录
            camera_id: 摄像头ID
            sensor_data: 传感器数据
            threshold: 覆盖率阈值
            system_type: 系统类型
            standard_image_path: 标准图片路径
            current_time: 当前时间
            
        Returns:
            tuple: (覆盖率, 分析结果, 警报状态, 是否异常, 图片路径, 分析建议, 故障类型列表)
        """
        logging.info(f"耙斗处理器开始处理帧 - 摄像头ID: {camera_id}, 帧计数: {frame_count}")
        
        # 使用基类的辅助方法准备帧
        resized_frame, frame_path = self._prepare_frame(frame, save_dir, camera_id, current_time)
        failure_reasons_type = []  # 出现故障的类型
        
        # 处理图像比较
        response_dict = self._process_image_comparison(
            resized_frame, frame_path, standard_image_path, system_type
        )
        # --- 新增的防御性检查 ---
        if not isinstance(response_dict, dict):
            logging.error(f"帧 {frame_count} - 图像分析模型未能返回有效的字典结果。收到的结果: {response_dict}")
            # 返回一个明确的错误状态，而不是让程序崩溃
            return (
                99,  # coverage_float: 标记为异常
                "图像分析模型处理失败",  # all_situation_analysis
                "WARNING",  # alarm_status: 字符串形式的报警状态
                True,  # is_abnormal: 布尔值表示异常
                str(frame_path),  # frame_path: 确保是字符串
                "系统内部错误：图像分析模型未能返回有效结果，请检查模型服务状态。",  # analysis_result
                ["模型处理失败"]  # failure_reasons_type
            )
        # 获取设备列表和区域计数
        devices_list = response_dict.get('devices', [])  # 是否倾斜
        region_counts = response_dict.get('region_counts', {})  # 是否卡住
        
        # 获取是否跳过了倾斜检测的标识
        self.is_incline_detection_skipped = response_dict.get('is_incline_detection_skipped', False)
        
        # 记录设备列表和区域计数的详细日志
        logging.info(f"帧 {frame_count} - 检测到的设备: {devices_list}")
        logging.info(f"帧 {frame_count} - 区域计数: {region_counts}")
        if self.is_incline_detection_skipped:
            logging.info(f"帧 {frame_count} - 跳过了倾斜检测")
        
        # 处理重置机制
        self._handle_reset_mechanisms()
        
        # 处理倾斜检测和卡住检测
        detected_device_ids = [device['device_id'] for device in devices_list]
        incline_results = self._handle_incline_detection(devices_list, detected_device_ids, frame_count) # 倾斜检测
        stuck_results = self._handle_stuck_detection(region_counts, frame_count) # 卡住检测
        
        # 合并检测结果
        status_analysis = incline_results['status_analysis']
        has_inclined_devices = incline_results['has_inclined_devices']
        has_new_incline_alarm = incline_results['has_new_incline_alarm']
        is_incline_reset_alarm = incline_results['is_incline_reset_alarm']  # 新增
        actual_device_status = 1  # 默认正常
        
        stuck_regions = stuck_results['stuck_regions']
        has_new_stuck_alarm = stuck_results['has_new_stuck_alarm']
        has_stuck_region = stuck_results['has_stuck_region']
        is_stuck_reset_alarm = stuck_results['is_stuck_reset_alarm']  # 新增
        
        # 更新设备状态和故障类型
        if has_inclined_devices:
            actual_device_status = 99  # 异常
            failure_reasons_type.append('耙斗倾斜')
        
        if has_stuck_region:
            actual_device_status = 99  # 异常
            failure_reasons_type.append('耙斗卡住')
        
        # 确定是否有新报警
        new_alarms = has_new_incline_alarm or has_new_stuck_alarm
        
        # 根据新的报警设置覆盖率
        coverage_float = 99 if new_alarms else 1
        
        if new_alarms:
            # 记录详细的报警源信息
            alarm_sources = []
            if has_new_incline_alarm:
                alarm_sources.append("新的耙斗倾斜报警")
            if has_new_stuck_alarm:
                alarm_sources.append("新的耙斗卡住报警")
            logging.info(f"设置 coverage_float=99，报警源: {', '.join(alarm_sources)}")
        
        # 格式化结果
        result_data = {
            'status_analysis': status_analysis,
            'has_inclined_devices': has_inclined_devices,
            'has_new_incline_alarm': has_new_incline_alarm,
            'is_incline_reset_alarm': is_incline_reset_alarm,  # 新增
            'stuck_regions': stuck_regions,
            'has_new_stuck_alarm': has_new_stuck_alarm,
            'is_stuck_reset_alarm': is_stuck_reset_alarm,  # 新增
            'new_alarms': new_alarms,
            'actual_device_status': actual_device_status,
            'failure_reasons_type': failure_reasons_type,
            'coverage_float': coverage_float,
            'frame_path': frame_path
        }
        
        formatted_results = self._format_results(result_data)
        
        # 构建返回结果
        result = (
            formatted_results['coverage_float'],
            formatted_results['all_situation_analysis'],
            formatted_results['alarm_status'],  # 字符串形式的报警状态
            formatted_results['is_abnormal'],   # 布尔值形式的异常状态
            formatted_results['frame_path'],
            formatted_results['analysis_result'],
            formatted_results['failure_reasons_type']
        )
        
        return result

    def _handle_reset_mechanisms(self):
        """处理报警重置机制"""
        # 不再使用全局倾斜检测报警重置计数器和卡住检测报警重置计数器
        # 每个设备和区域现在有自己独立的重置计数器
        # 倾斜检测的重置在 _handle_incline_detection 方法中针对每个设备单独处理
        # 卡住检测的重置在 _handle_stuck_detection 方法中针对每个区域单独处理
        logging.info("使用独立的设备和区域重置计数器，不再使用全局重置计数器")

    def _get_position_description(self, device_id):
        """根据设备ID获取位置描述"""
        return self.device_positions.get(device_id, f"设备{device_id}")

    def _get_region_description(self, region_name):
        """根据区域名称获取区域描述"""
        return self.region_descriptions.get(region_name, region_name.replace("region-0", "区域"))

    def _handle_incline_detection(self, devices_list, detected_device_ids, frame_count):
        """处理倾斜检测
        
        Args:
            devices_list: 检测到的设备列表
            detected_device_ids: 检测到的设备ID列表
            frame_count: 当前帧计数
            
        Returns:
            dict: 倾斜检测结果
        """
        status_analysis = []
        has_inclined_devices = False
        has_new_incline_alarm = False
        is_incline_reset_alarm = False  # 新增：标记是否为重置触发的报警
        
        # 检查是否跳过了倾斜检测
        is_incline_detection_skipped = hasattr(self, 'is_incline_detection_skipped') and self.is_incline_detection_skipped
        
        # 处理未被检测到的设备 - 保持其之前的状态，不盲目重置
        for device_id in self.processor.device_incline_state:
            # 确保每个设备都有重置计数器字段和上一次实际检测状态字段
            if "reset_counter" not in self.processor.device_incline_state[device_id]:
                self.processor.device_incline_state[device_id]["reset_counter"] = 0
            if "last_actual_detection_status" not in self.processor.device_incline_state[device_id]:
                self.processor.device_incline_state[device_id]["last_actual_detection_status"] = self.processor.device_incline_state[device_id]["last_status"]
            
            if device_id not in detected_device_ids:
                # 获取设备的上一次状态
                last_status = self.processor.device_incline_state[device_id]["last_status"]
                # 获取设备的上一次实际检测状态
                last_actual_detection_status = self.processor.device_incline_state[device_id]["last_actual_detection_status"]
                logging.info(f"设备 {device_id} 未在当前帧中检测到，保持之前的状态: {last_status}，上一次实际检测状态: {last_actual_detection_status}")
                
                # 确定位置描述
                position = self._get_position_description(device_id)
                
                # 根据之前的状态处理
                if last_status == "incline":
                    # 如果设备之前是倾斜状态，保持倾斜状态并记入倾斜设备列表
                    has_inclined_devices = True
                    
                    # 根据是否跳过倾斜检测生成不同的状态描述
                    if is_incline_detection_skipped:
                        status_analysis.append(f"{position}耙斗状态为{last_status}(本次跳过倾斜检测)")
                    else:
                        status_analysis.append(f"{position}耙斗状态为{last_status}(未在当前帧检测到，保持之前状态)")
                    
                    # 如果设备处于倾斜状态且已触发报警，且本次没有跳过倾斜检测，才增加其重置计数器
                    if self.processor.device_incline_state[device_id]["alarm_triggered"] and not is_incline_detection_skipped:
                        self.processor.device_incline_state[device_id]["reset_counter"] += 1
                        logging.info(f"设备 {device_id} 的重置计数器增加到 {self.processor.device_incline_state[device_id]['reset_counter']}")
                        
                        # 检查是否达到重置阈值
                        if (self.processor.incline_reset_enabled and 
                            self.processor.device_incline_state[device_id]["reset_counter"] >= self.processor.incline_reset_threshold):
                            # 重置该设备的报警状态和计数器，并标记为重置报警
                            self.processor.device_incline_state[device_id]["alarm_triggered"] = False
                            self.processor.device_incline_state[device_id]["reset_counter"] = 0
                            has_new_incline_alarm = True
                            is_incline_reset_alarm = True  # 标记为重置触发的报警
                            logging.info(f"设备 {device_id} 的倾斜检测报警状态已重置，触发重置报警")
                else:
                    # 如果设备之前是正常状态，保持正常状态
                    # 根据是否跳过倾斜检测生成不同的状态描述
                    if is_incline_detection_skipped:
                        status_analysis.append(f"{position}耙斗状态为{last_status}(本次跳过倾斜检测)")
                    else:
                        status_analysis.append(f"{position}耙斗状态为{last_status}(未在当前帧检测到，保持之前状态)")
                    
                    logging.info(f"设备 {device_id} 之前状态正常，保持正常状态")
                    # 如果设备处于正常状态，重置其计数器
                    if self.processor.device_incline_state[device_id]["reset_counter"] > 0:
                        self.processor.device_incline_state[device_id]["reset_counter"] = 0
                        logging.info(f"设备 {device_id} 状态正常，重置计数器为0")
        
        # 遍历所有检测到的设备
        for device in devices_list:
            device_id = device['device_id']
            status = device['status']
            
            # 记录每个设备的状态日志
            logging.info(f"帧 {frame_count} - 设备 {device_id}: 状态={status}, 置信度={device['confidence']:.2f}")
            
            # 确保设备ID存在于状态字典中，并包含重置计数器和上一次实际检测状态
            if device_id not in self.processor.device_incline_state:
                self.processor.device_incline_state[device_id] = {
                    "alarm_triggered": False, 
                    "last_status": "unknown", 
                    "reset_counter": 0,
                    "last_actual_detection_status": "unknown"
                }
            elif "reset_counter" not in self.processor.device_incline_state[device_id]:
                self.processor.device_incline_state[device_id]["reset_counter"] = 0
            elif "last_actual_detection_status" not in self.processor.device_incline_state[device_id]:
                self.processor.device_incline_state[device_id]["last_actual_detection_status"] = self.processor.device_incline_state[device_id]["last_status"]
            
            # 获取设备的上一次状态
            last_status = self.processor.device_incline_state[device_id]["last_status"]
            # 获取设备的上一次实际检测状态
            last_actual_detection_status = self.processor.device_incline_state[device_id]["last_actual_detection_status"]
            
            # 如果没有跳过倾斜检测，更新上一次实际检测状态
            if not is_incline_detection_skipped:
                self.processor.device_incline_state[device_id]["last_actual_detection_status"] = status
            
            # 检查是否有倾斜设备
            if status == 'incline':
                has_inclined_devices = True
                
                # 如果之前未触发报警或者从正常状态变回倾斜状态，则标记为新报警
                # 注意：如果跳过了倾斜检测，我们应该使用上一次实际检测的状态进行比较
                compare_status = last_actual_detection_status if is_incline_detection_skipped else last_status
                
                if not self.processor.device_incline_state[device_id]["alarm_triggered"] or (compare_status == "no_incline" and status == "incline"):
                    has_new_incline_alarm = True
                    self.processor.device_incline_state[device_id]["alarm_triggered"] = True
                    # 重置该设备的重置计数器
                    self.processor.device_incline_state[device_id]["reset_counter"] = 0
                    logging.warning(f"设备 {device_id} 倾斜，触发报警 (上一状态: {compare_status})")
                else:
                    # 设备仍处于倾斜状态，只有在没有跳过倾斜检测的情况下才增加重置计数器
                    if not is_incline_detection_skipped:
                        self.processor.device_incline_state[device_id]["reset_counter"] += 1
                        logging.info(f"设备 {device_id} 仍处于倾斜状态，重置计数器增加到 {self.processor.device_incline_state[device_id]['reset_counter']}")
                        
                        # 检查是否达到重置阈值
                        if (self.processor.incline_reset_enabled and 
                            self.processor.device_incline_state[device_id]["reset_counter"] >= self.processor.incline_reset_threshold):
                            # 重置该设备的报警状态和计数器，并标记为重置报警
                            self.processor.device_incline_state[device_id]["alarm_triggered"] = False
                            self.processor.device_incline_state[device_id]["reset_counter"] = 0
                            has_new_incline_alarm = True
                            is_incline_reset_alarm = True  # 标记为重置触发的报警
                            logging.info(f"设备 {device_id} 的倾斜检测报警状态已重置，触发重置报警")
                    else:
                        logging.info(f"设备 {device_id} 仍处于倾斜状态，但由于跳过了倾斜检测，不增加重置计数器")
            else:
                # 如果设备从倾斜恢复到正常状态，重置报警状态和计数器
                # 注意：如果跳过了倾斜检测，我们应该使用上一次实际检测的状态进行比较
                compare_status = last_actual_detection_status if is_incline_detection_skipped else last_status
                
                if compare_status == "incline" and status == "no_incline":
                    self.processor.device_incline_state[device_id]["alarm_triggered"] = False
                    self.processor.device_incline_state[device_id]["reset_counter"] = 0
                    logging.info(f"设备 {device_id} 从倾斜恢复到正常状态，重置报警状态和计数器")
            
            # 更新设备上一次状态
            self.processor.device_incline_state[device_id]["last_status"] = status
            
            # 添加状态分析
            position = self._get_position_description(device_id)
            status_analysis.append(f"{position}耙斗状态为{status}")
        
        return {
            'status_analysis': status_analysis,
            'has_inclined_devices': has_inclined_devices,
            'has_new_incline_alarm': has_new_incline_alarm,
            'is_incline_reset_alarm': is_incline_reset_alarm  # 新增返回值
        }

    def _handle_stuck_detection(self, region_counts, frame_count):
        """处理卡住检测
        
        Args:
            region_counts: 区域计数字典
            frame_count: 当前帧计数
            
        Returns:
            dict: 卡住检测结果
        """
        stuck_regions = []
        has_new_stuck_alarm = False
        has_stuck_region = False
        is_stuck_reset_alarm = False  # 新增：标记是否为重置触发的报警
        
        # 记录原始区域计数信息
        logging.info(f"帧 {frame_count} - 原始区域计数信息: {region_counts}")
        
        # 检查是否有区域不在结果中，这可能表示数据问题
        missing_regions = set(self.processor.region_state.keys()) - set(region_counts.keys())
        if missing_regions:
            logging.warning(f"帧 {frame_count} - 以下区域在region_counts中缺失: {missing_regions}")
        
        # 遍历所有预定义的区域，而不是只遍历region_counts中的区域
        for region_name in self.processor.region_state:
            # 确保每个区域都有重置计数器字段
            if "reset_counter" not in self.processor.region_state[region_name]:
                self.processor.region_state[region_name]["reset_counter"] = 0
                
            # 获取区域计数，如果region_counts中不存在该区域，则视为0（无耙斗）
            count = region_counts.get(region_name, 0)
            region_state = self.processor.region_state[region_name]
            
            # 如果区域在region_counts中缺失，记录特殊日志
            if region_name not in region_counts:
                logging.warning(f"帧 {frame_count} - 区域 {region_name} 在检测结果中缺失，假定值为0（无耙斗）")
            
            # 记录每个区域当前状态的详细日志
            logging.info(f"帧 {frame_count} - 区域 {region_name}: count={count}, timer={region_state['timer']}, last_state={region_state['last_state']}, alarm_triggered={region_state['alarm_triggered']}, reset_counter={region_state.get('reset_counter', 0)}")
            
            # 首次运行时初始化状态
            if region_state["last_state"] is None:
                region_state["last_state"] = count
                logging.info(f"区域 {region_name} 初始化状态为 {count}")
                continue
            
            # 从1变为0时开始计时
            if region_state["last_state"] == 1 and count == 0:
                region_state["timer"] += 1
                logging.info(f"区域 {region_name} 从有耙斗变为无耙斗，计时器增加到 {region_state['timer']}")
            # 如果从0变为1或保持1，重置计时器
            elif count == 1:
                if region_state["timer"] > 0:
                    logging.info(f"区域 {region_name} 检测到耙斗，计时器从 {region_state['timer']} 重置为 0")
                region_state["timer"] = 0
                region_state["reset_counter"] = 0  # 重置区域的重置计数器
                # 添加重置报警状态的逻辑，让耙斗重新出现后可以再次触发报警
                if region_state["alarm_triggered"]:
                    region_state["alarm_triggered"] = False
                    logging.info(f"区域 {region_name} 检测到耙斗重新出现，报警状态已重置，允许再次触发报警")
            # 如果一直是0，且已经开始计时，继续计时
            elif region_state["last_state"] == 0 and count == 0 and region_state["timer"] > 0:
                region_state["timer"] += 1
                logging.info(f"区域 {region_name} 持续无耙斗，计时器增加到 {region_state['timer']}")
            
            # 检查是否启用了重置机制且超过重置阈值
            if self.processor.reset_enabled and self.processor.reset_threshold and region_state["timer"] >= self.processor.reset_threshold:
                logging.info(f"区域 {region_name} 的计时器 {region_state['timer']} 已达到重置阈值 {self.processor.reset_threshold}，重新开始检测")
                region_state["timer"] = 0
                region_state["last_state"] = count
                continue
                
            # 更新状态
            region_state["last_state"] = count
            
            # 打印当前区域状态信息，用于调试
            logging.info(f"区域 {region_name}: count={count}, timer={region_state['timer']}, last_state={region_state['last_state']}")
            
            # 检查是否超过卡住阈值(如果启用了重置机制还需要判断是否未超过重置阈值)
            if region_state["timer"] >= self.processor.stuck_threshold:
                # 如果启用了重置机制，则只在卡住阈值和重置阈值之间报警
                if not self.processor.reset_enabled or not self.processor.reset_threshold or region_state["timer"] < self.processor.reset_threshold:
                    # 只要区域卡住就添加到故障类型列表中，无论是否首次触发
                    if not has_stuck_region:  # 只添加一次"耙斗卡住"到故障类型列表
                        has_stuck_region = True
                        logging.warning(f"检测到耙斗卡住状态，添加到故障类型列表")
                    # 只有当该区域之前未触发报警时才标记为新报警
                    if not region_state["alarm_triggered"]:
                        stuck_regions.append(region_name)
                        region_state["alarm_triggered"] = True
                        has_new_stuck_alarm = True
                        # 重置该区域的重置计数器
                        region_state["reset_counter"] = 0
                        logging.warning(f"区域 {region_name} 卡住，首次触发报警，计时器值为 {region_state['timer']}")
                    else:
                        # 区域已经报警过，但仍然需要记录在stuck_regions中用于显示
                        stuck_regions.append(region_name)
                        # 增加该区域的重置计数器
                        region_state["reset_counter"] += 1
                        logging.info(f"区域 {region_name} 仍处于卡住状态，已报警过，计时器值为 {region_state['timer']}，重置计数器增加到 {region_state['reset_counter']}")
                        
                        # 检查是否达到重置阈值
                        if (self.processor.stuck_reset_enabled and 
                            region_state["reset_counter"] >= self.processor.stuck_reset_threshold):
                            # 重置该区域的报警状态和计数器，并标记为重置报警
                            region_state["alarm_triggered"] = False
                            region_state["reset_counter"] = 0
                            has_new_stuck_alarm = True
                            is_stuck_reset_alarm = True  # 标记为重置触发的报警
                            logging.info(f"区域 {region_name} 的卡住检测报警状态已重置，触发重置报警")
        
        return {
            'stuck_regions': stuck_regions,
            'has_new_stuck_alarm': has_new_stuck_alarm,
            'has_stuck_region': has_stuck_region,
            'is_stuck_reset_alarm': is_stuck_reset_alarm  # 新增返回值
        }

    def _format_results(self, data):
        """格式化结果
        
        Args:
            data: 包含处理结果的字典
            
        Returns:
            dict: 格式化后的结果
        """
        status_analysis = data['status_analysis']
        has_inclined_devices = data['has_inclined_devices']
        has_new_incline_alarm = data['has_new_incline_alarm']
        is_incline_reset_alarm = data.get('is_incline_reset_alarm', False)  # 新增
        stuck_regions = data['stuck_regions']
        has_new_stuck_alarm = data['has_new_stuck_alarm']
        is_stuck_reset_alarm = data.get('is_stuck_reset_alarm', False)  # 新增
        new_alarms = data['new_alarms']
        actual_device_status = data['actual_device_status']
        failure_reasons_type = data['failure_reasons_type']
        coverage_float = data['coverage_float']
        frame_path = data['frame_path']
        
        # 检查是否跳过了倾斜检测
        is_incline_detection_skipped = hasattr(self, 'is_incline_detection_skipped') and self.is_incline_detection_skipped
        
        # 生成分析结果字符串
        if not status_analysis:
            base_analysis = "当前未识别到任何耙斗设备"
            logging.warning("当前未识别到任何耙斗设备")
        else:
            # 如果跳过了倾斜检测，则不显示倾斜相关内容
            if is_incline_detection_skipped:
                base_analysis = ""  # 不显示倾斜状态
                logging.info("跳过倾斜检测，不显示倾斜状态信息")
            else:
                # 将英文状态转换为中文描述
                status_analysis_translated = []
                for analysis in status_analysis:
                    analysis = analysis.replace("no_incline", "没有倾斜")
                    analysis = analysis.replace("incline", "倾斜")
                    
                    # 如果包含"未在当前帧检测到"这样的技术细节，进行简化
                    if "(未在当前帧检测到，保持之前状态)" in analysis:
                        parts = analysis.split("状态为")
                        if len(parts) >= 2:
                            simplified_status = parts[1].split("(")[0]  # 只保留状态，去掉括号内容
                            analysis = f"{parts[0]}状态为{simplified_status}"
                    
                    status_analysis_translated.append(analysis)
                
                base_analysis = "从左到右当前的" + "，".join(status_analysis_translated)
                logging.info(f"设备状态分析: {base_analysis}")
        
        # 准备区域描述信息（即使没有卡住的区域）
        stuck_regions_text = ""
        if stuck_regions:
            # 将区域编号转换为更直观的位置描述
            region_descriptions = []
            for region in stuck_regions:
                region_desc = self._get_region_description(region)
                region_descriptions.append(region_desc)
            
            stuck_regions_text = "、".join(region_descriptions)
            logging.info(f"卡住区域: {stuck_regions_text}")
        else:
            # 添加明确的"未卡住"判断输出
            stuck_regions_text = "当前检测到的耙斗没有发生卡住的问题"
            logging.info("未检测到卡住区域")
        
        # 根据是否为新报警来调整情况分析的描述，并添加详细的报警源信息
        alarm_source_detail = []
        reset_alarm_prefix = ""  # 新增：重置报警的前缀
        
        # 判断是否为重置触发的报警，并设置相应的前缀
        if is_incline_reset_alarm or is_stuck_reset_alarm:
            reset_alarm_prefix = "【报警的再次提示】"
        
        if has_new_incline_alarm and not is_incline_detection_skipped:
            incline_text = "耙斗倾斜"
            if is_incline_reset_alarm:
                incline_text = f"{reset_alarm_prefix}{incline_text}"
            alarm_source_detail.append(incline_text)
            
        if has_new_stuck_alarm and stuck_regions:
            stuck_text = f"{stuck_regions_text}耙斗卡住"
            if is_stuck_reset_alarm:
                stuck_text = f"{reset_alarm_prefix}{stuck_text}"
            alarm_source_detail.append(stuck_text)
        
        # 处理所有报警情况（包括仅倾斜报警）
        all_situation_analysis = ""
        if new_alarms:
            # 组合所有新报警源
            if alarm_source_detail:
                alarm_type_text = "新检测到的异常"
                if is_incline_reset_alarm or is_stuck_reset_alarm:
                    alarm_type_text = "异常状态持续"
                new_alarm_text = f"{alarm_type_text}: {', '.join(alarm_source_detail)}"
            elif stuck_regions:
                stuck_alarm_text = f"检测到{stuck_regions_text}可能发生耙斗卡住！"
                if is_stuck_reset_alarm:
                    stuck_alarm_text = f"{reset_alarm_prefix}{stuck_alarm_text}"
                new_alarm_text = stuck_alarm_text
            else:
                new_alarm_text = "检测到异常状态！"
                if is_incline_reset_alarm or is_stuck_reset_alarm:
                    new_alarm_text = f"{reset_alarm_prefix}{new_alarm_text}"
            
            # 如果跳过了倾斜检测，只显示卡住相关信息
            if is_incline_detection_skipped:
                if has_new_stuck_alarm and stuck_regions:
                    stuck_alarm_text = f"检测到{stuck_regions_text}可能发生耙斗卡住！"
                    if is_stuck_reset_alarm:
                        stuck_alarm_text = f"{reset_alarm_prefix}{stuck_alarm_text}"
                    all_situation_analysis = stuck_alarm_text
                else:
                    all_situation_analysis = stuck_regions_text  # 只显示卡住状态
            else:
                all_situation_analysis = f"{base_analysis}。{new_alarm_text}"
            
            log_level = logging.warning if not (is_incline_reset_alarm or is_stuck_reset_alarm) else logging.info
            log_level(f"报警: {all_situation_analysis}")
        elif stuck_regions or (has_inclined_devices and not is_incline_detection_skipped):  # 处理持续报警
            ongoing_issues = []
            if stuck_regions:
                ongoing_issues.append(f"{stuck_regions_text}仍有耙斗卡住状态")
            if has_inclined_devices and not is_incline_detection_skipped:
                ongoing_issues.append("仍有耙斗倾斜状态")
            
            # 如果跳过了倾斜检测，只显示卡住相关信息
            if is_incline_detection_skipped:
                if stuck_regions:
                    all_situation_analysis = f"{stuck_regions_text}仍有耙斗卡住状态，但已发出报警提示。"
                else:
                    all_situation_analysis = stuck_regions_text  # 只显示卡住状态
            else:
                all_situation_analysis = f"{base_analysis}。{'、'.join(ongoing_issues)}，但已发出报警提示。"
            
            logging.info(f"持续报警: {all_situation_analysis}")
        else:
            # 正常状态下，根据是否跳过倾斜检测决定输出内容
            if is_incline_detection_skipped:
                all_situation_analysis = stuck_regions_text  # 只显示卡住状态
            else:
                all_situation_analysis = f"{base_analysis}。{stuck_regions_text}"
            
            if coverage_float == 1:  # 只有在没有其他报警时记录正常状态
                logging.info(f"正常状态: {all_situation_analysis}")
        
        # 更新分析建议
        analysis_result = "所有设备运行正常"
        if actual_device_status == 99:  # 使用actual_device_status判断设备实际状态
            # 构建分析建议，明确区分不同类型的新报警
            suggestion_parts = []
            
            if new_alarms:  # 有新报警
                # 处理卡住报警
                if has_new_stuck_alarm and stuck_regions:
                    # 同样为分析建议中的区域添加位置描述
                    region_descriptions = []
                    for region in stuck_regions:
                        region_desc = self._get_region_description(region)
                        region_descriptions.append(region_desc)
                    
                    stuck_suggestion = f"新检测到{'、'.join(region_descriptions)}的耙斗可能卡住"
                    if is_stuck_reset_alarm:
                        stuck_suggestion = f"{reset_alarm_prefix}{stuck_suggestion}"
                    suggestion_parts.append(stuck_suggestion)
                
                # 处理倾斜报警（如果没有跳过倾斜检测）
                if has_new_incline_alarm and not is_incline_detection_skipped:
                    incline_suggestion = "新检测到耙斗倾斜"
                    if is_incline_reset_alarm:
                        incline_suggestion = f"{reset_alarm_prefix}{incline_suggestion}"
                    suggestion_parts.append(incline_suggestion)
                
                # 如果没有新报警但有持续的报警，也要处理
                if not suggestion_parts:
                    if stuck_regions and not has_new_stuck_alarm:
                        suggestion_parts.append("耙斗卡住状态持续")
                    if has_inclined_devices and not has_new_incline_alarm and not is_incline_detection_skipped:
                        suggestion_parts.append("耙斗倾斜状态持续")
                
                # 组合最终建议
                if suggestion_parts:
                    suggestion_text = "、".join(suggestion_parts)
                    # 根据是否为重置报警调整建议文本
                    if is_incline_reset_alarm or is_stuck_reset_alarm:
                        analysis_result = f"{suggestion_text}。设备异常状态持续时间较长，请运维人员尽快前往现场对设备运行状态进行评估，并依据流程展开处理。"
                    else:
                        analysis_result = f"{suggestion_text}。请运维人员尽快前往现场对设备运行状态进行评估，并依据流程展开处理。"
                else:
                    # 以防万一有新报警但未被上面的逻辑捕获
                    analysis_result = "检测到设备异常，请运维人员尽快前往现场对设备运行状态进行评估。"
            else:  # 无新报警但仍有异常状态
                # 构建持续异常状态的分析建议
                ongoing_issues = []
                
                # 处理持续的卡住状态
                if stuck_regions:
                    region_descriptions = []
                    for region in stuck_regions:
                        region_desc = self._get_region_description(region)
                        region_descriptions.append(region_desc)
                    
                    ongoing_issues.append(f"{'、'.join(region_descriptions)}仍有耙斗卡住状态")
                
                # 处理持续的倾斜状态（如果没有跳过倾斜检测）
                if has_inclined_devices and not is_incline_detection_skipped:
                    ongoing_issues.append("设备仍有倾斜状态")
                
                # 组合最终建议
                if ongoing_issues:
                    analysis_result = "、".join(ongoing_issues) + "，已发出报警提示"
                else:
                    analysis_result = "所有设备运行正常"
            
            logging.info(f"分析建议: {analysis_result}")
        else:
            analysis_result = "所有设备运行正常"
            logging.info("分析结果: 所有设备运行正常")
        
        # 根据设备状态确定警报状态 - 现在同时生成字符串和布尔值
        is_abnormal = actual_device_status == 99  # 如果状态为99则是异常(True)，否则为正常(False)
        alarm_status = 'WARNING' if is_abnormal else 'NO_ALARM'  # 保留字符串格式的警报状态
        result = {
            'coverage_float': coverage_float,
            'all_situation_analysis': all_situation_analysis,
            'new_alarms': new_alarms,
            'is_abnormal': is_abnormal,  # 布尔值表示异常状态
            'alarm_status': alarm_status,  # 字符串表示报警状态
            'frame_path': str(frame_path),
            'analysis_result': analysis_result,
            'failure_reasons_type': failure_reasons_type
        }
        return result