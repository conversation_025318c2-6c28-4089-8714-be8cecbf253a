import logging
import re
import cv2
from pathlib import Path
from typing import Dict, List, Tuple, Any

from server.utils.handlers.base_handler import BaseFrameHandler
from llms.llm_api_server import llm_process_image_aerobic


class AerobicPoolHandler(BaseFrameHandler):
    """好氧池处理器类
    
    负责处理好氧池相关的视频帧分析
    """
    
    def process_frame(self, frame, frame_count, save_dir, camera_id, sensor_data,
                      threshold, system_type, standard_image_path=None, current_time=None) -> Tuple:
        """处理好氧池相关的视频帧
        
        Args:
            frame: 视频帧图像数据
            frame_count: 帧计数
            save_dir: 保存目录
            camera_id: 摄像头ID
            sensor_data: 传感器数据
            threshold: 覆盖率阈值
            system_type: 系统类型
            standard_image_path: 标准图片路径
            current_time: 当前时间
            
        Returns:
            tuple: (覆盖率, 分析结果, 警报状态, 是否异常, 图片路径, 分析建议, 故障类型列表)
        """
        logging.info(f"好氧池处理器开始处理帧 - 摄像头ID: {camera_id}, 帧计数: {frame_count}")
        
        # frame_filename = f"frame_{camera_id}_{current_time.strftime('%Y_%m_%d_%H_%M_%S')}.jpg"
        # frame_path = save_dir / frame_filename
        resized_frame, frame_path = self._prepare_frame(frame, save_dir, camera_id, current_time)
        failure_reasons_type = []  # 出现故障的类型
        # 下面的代码是调整图像尺寸为1600x900，但是因为已经调用_prepare_frame函数，所以不需要再调整图像尺寸
        # 调整图像尺寸为1600x900
        # resized_frame = cv2.resize(frame, (1600, 900), interpolation=cv2.INTER_AREA)
        
        # 保存调整后的帧
        # cv2.imwrite(str(frame_path), resized_frame)
        
        # 处理图像比较
        response_dict = self._process_image_comparison(
            resized_frame, frame_path, standard_image_path, system_type
        )
        
        # 分析泡沫覆盖率
        value = response_dict['当前泡沫覆盖率']
        number = re.search(r'\d+(\.\d+)?', value)
        if number is None:
            logging.warning(f"无法从泡沫覆盖率中提取数字: {value}")
            coverage_float = 0
        else:
            coverage_float = float(number.group(0))
        
        # 曝气头脱落
        down_air_bubble = response_dict.get('曝气头是否脱落或者损坏', '否')
        
        # 获取传感器数据
        sensor_data_do = sensor_data.get('DO', '-')
        sensor_data_mlss = sensor_data.get('MLSS', '-')
        
        if sensor_data_do == '-' and sensor_data_mlss == '-':
            analysis_result = "暂无法提供详细建议,字段信息不全或者出现异常值,请检查DO和MLSS的数值"
            all_situation_analysis = response_dict.get('情况分析', '')
        else:
            analysis_result = llm_process_image_aerobic(
                filter_result=response_dict.get('情况分析', ''), 
                DO=sensor_data.get('DO'), 
                MLSS=sensor_data.get('MLSS'), 
                bubble_area=coverage_float, 
                system_type=system_type
            )
            all_situation_analysis = response_dict.get('情况分析', '')
        
        # 获取上一次记录的泡沫面积并检查是否有增长
        previous_area = self.processor.previous_bubble_area.get(camera_id, 0)
        
        # 添加各种异常情况提示信息
        situation_changes = []
        
        # 检查曝气头脱落状态
        if down_air_bubble == '有':
            analysis_result += "\n警告：检测到曝气头脱落或损坏，需要立即进行检修。"
            situation_changes.append("检测到曝气头脱落或损坏，需要立即进行检修")
            failure_reasons_type.append('曝气头脱落或损坏')
        
        # 检查泡沫面积增长情况
        if previous_area > 0 and coverage_float > previous_area:
            # 初始化增加计数器（如果不存在）
            if camera_id not in self.processor.bubble_increase_counter:
                self.processor.bubble_increase_counter[camera_id] = 0
            
            # 增加计数
            self.processor.bubble_increase_counter[camera_id] += 1
            
            # 如果连续增加次数达到阈值
            if self.processor.bubble_increase_counter[camera_id] >= self.processor.bubble_increase_threshold:
                # 添加泡沫面积增长信息到分析结果
                analysis_result += f"\n注意：泡沫面积连续{self.processor.bubble_increase_counter[camera_id]}次增加，最近从 {previous_area}% 增加到 {coverage_float}%，可能表明工艺状况正在恶化，建议及时检查。"
                situation_changes.append(f"泡沫面积连续增长情况：当前 {coverage_float}%，上次 {previous_area}%，增长了 {coverage_float - previous_area}%")
                failure_reasons_type.append('泡沫面积增长')
            else:
                # 记录增长但还未达到报警阈值
                logging.info(f"摄像头 {camera_id} 泡沫面积增加 {self.processor.bubble_increase_counter[camera_id]}/{self.processor.bubble_increase_threshold} 次: {previous_area}% -> {coverage_float}%")
        else:
            # 如果没有增加，重置计数器
            self.processor.bubble_increase_counter[camera_id] = 0
        
        # 将变化情况添加到情况分析
        if situation_changes:
            all_situation_analysis += "\n\n异常状况摘要：\n- " + "\n- ".join(situation_changes)
        
        # 获取上一次记录的泡沫面积
        previous_area = self.processor.previous_bubble_area.get(camera_id, 0)
        
        # 初始化警报状态和异常标志
        alarm_status_flag = 0
        is_abnormal = False
        
        # 检查曝气头脱落状态，无论泡沫面积情况如何，曝气头脱落都需要触发报警
        if down_air_bubble == '有':
            alarm_status_flag = 1
            is_abnormal = True
            logging.info(f"摄像头 {camera_id} 检测到曝气头脱落，触发报警")
        
        # 检查泡沫面积连续增长情况，即使曝气头已经触发了报警，也需要检查并记录泡沫面积变化
        if self.processor.bubble_increase_counter.get(camera_id, 0) >= self.processor.bubble_increase_threshold:
            alarm_status_flag = 1
            is_abnormal = True
            logging.info(f"摄像头 {camera_id} 泡沫面积连续{self.processor.bubble_increase_counter[camera_id]}次增加，触发报警")
        
        # 更新上一次泡沫面积记录
        self.processor.previous_bubble_area[camera_id] = coverage_float
        
        logging.info(f"好氧池处理器处理完成 - 摄像头ID: {camera_id}, 泡沫覆盖率: {coverage_float}%, 警报状态: {alarm_status_flag}")
        if failure_reasons_type:
            logging.warning(f"检测到故障类型: {', '.join(failure_reasons_type)}")
        
        return (coverage_float, all_situation_analysis, alarm_status_flag, is_abnormal,
                str(frame_path), analysis_result, failure_reasons_type) 