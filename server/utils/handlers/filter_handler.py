import logging
import re
import cv2
from pathlib import Path
from typing import Dict, List, Tuple, Any
from datetime import datetime, timedelta

from server.utils.handlers.base_handler import BaseFrameHandler
from llms.llm_api_server import llm_process_image_filter
from llms.vllm_api_server import process_image
from config_file import config


class FilterHandler(BaseFrameHandler):
    """滤池处理器类

    负责处理滤池相关的视频帧分析
    """

    def _check_filter_analysis_conditions(self, frame):
        """使用视觉大模型检查图像是否满足滤池分析条件

        调用视觉大模型判断当前图片中是否有水面存在，且水面无遮挡、完整，满足识别条件

        Args:
            frame: 输入的图像帧

        Returns:
            tuple: (是否满足条件, 分析结果描述)
        """
        try:
            logging.info("开始使用视觉大模型检查滤池分析条件...")

            # 调用视觉大模型进行预判断
            vlm_result = process_image(frame, 'system_prompt_is_filter')

            if not vlm_result:
                logging.error("视觉大模型返回空结果，默认认为满足条件")
                return True, "视觉大模型分析失败，默认继续分析"

            # 解析视觉大模型的结果
            is_suitable = vlm_result.get('是否满足识别条件', '否')

            if is_suitable == '是':
                logging.info("视觉大模型判断：图像满足滤池分析条件")
                return True, "图像中存在完整的水面，满足识别条件"
            else:
                logging.info("视觉大模型判断：图像不满足滤池分析条件")
                return False, "图像中水面不完整或存在遮挡，不满足识别条件"

        except Exception as e:
            logging.error(f"视觉大模型预判断过程中出现错误: {str(e)}")
            # 出现错误时，默认认为满足条件，避免误判
            return True, f"预判断过程出现错误: {str(e)}，默认继续分析"

    def process_frame(self, frame, frame_count, save_dir, camera_id, sensor_data,
                      threshold, system_type, standard_image_path=None, current_time=None) -> Tuple:
        """处理滤池相关的视频帧
        
        Args:
            frame: 视频帧图像数据
            frame_count: 帧计数
            save_dir: 保存目录
            camera_id: 摄像头ID
            sensor_data: 传感器数据
            threshold: 覆盖率阈值
            system_type: 系统类型
            standard_image_path: 标准图片路径
            current_time: 当前时间
            
        Returns:
            tuple: (覆盖率, 分析结果, 警报状态, 是否异常, 图片路径, 分析建议, 故障类型列表)
        """
        logging.info(f"滤池处理器开始处理帧 - 摄像头ID: {camera_id}, 帧计数: {frame_count}")

        # 先准备帧但不保存，用于分析
        resized_frame, potential_frame_path = self._prepare_frame_without_save(frame, save_dir, camera_id, current_time)
        failure_reasons_type = []  # 出现故障的类型

        # 步骤1：检查是否启用滤池预检查功能
        precheck_enabled = config.env.get('filter_precheck_enabled', True)

        if precheck_enabled:
            # 使用视觉大模型预判断是否满足滤池分析条件
            is_suitable_for_analysis, condition_description = self._check_filter_analysis_conditions(resized_frame)

            if not is_suitable_for_analysis:
                # 如果不满足分析条件，直接跳过后续识别流程
                logging.info(f"图像不满足滤池分析条件，跳过识别流程 - 摄像头ID: {camera_id}")
                logging.info(f"条件检查结果: {condition_description}")

                # 返回特定的"不满足分析条件"状态
                coverage_float = 1  # 设置为0表示无法计算覆盖率
                all_situation_analysis = condition_description
                analysis_result = "由于图像不满足分析条件，系统跳过了滤池识别流程"
                frame_path = ''  # 不保存图片

                # 确定警报状态 - 不满足条件不算异常，但需要特殊标记
                alarm_status_flag = 'NO_ALARM'  # 使用特殊状态标记
                is_abnormal = False  # 不满足条件不算异常

                logging.info(f"滤池处理器处理完成（不满足分析条件）- 摄像头ID: {camera_id}, 状态: {alarm_status_flag}")

                return (coverage_float, all_situation_analysis, alarm_status_flag, is_abnormal,
                        frame_path, analysis_result, failure_reasons_type)
            else:
                # 满足条件，继续原有的YOLO+小模型识别流程
                logging.info(f"图像满足滤池分析条件，继续识别流程 - 摄像头ID: {camera_id}")
                logging.info(f"条件检查结果: {condition_description}")
        else:
            # 预检查功能被禁用，直接继续识别流程
            logging.info(f"滤池预检查功能已禁用，直接进行识别 - 摄像头ID: {camera_id}")

        # 步骤2：继续原有的YOLO+小模型识别流程

        # 测试 assets/frame_4010_2024_12_06_17_48_30.jpg
        # resized_frame = cv2.imread('assets/5fdcf4d20eaadc93ff7258a968800c0a.png')
        # 处理图像比较 - 使用临时路径进行分析，同时获取YOLO标注图像
        response_dict, annotated_image = self._process_image_comparison_with_annotation(
            resized_frame, potential_frame_path, standard_image_path, system_type
        )
        
        # 分析结果
        analysis_result = llm_process_image_filter(filter_result=response_dict.get('你的思考', ''), system_type=system_type)
        value = response_dict['曝气头是否脱落或损坏']
        logging.info(f"---------------------------")
        logging.info(f"曝气头是否脱落或损坏: {value}")
        logging.info(f"---------------------------")
        # 测试
        # value = '是'
        # 根据分析结果决定是否保存图片
        if value == '是':
            # 异常情况，保存YOLO标注后的图片
            coverage_float = 99
            failure_reasons_type.append('曝气头可能出现脱落或损坏')
            # 保存YOLO标注后的图片
            if annotated_image is not None:
                save_success = self._save_annotated_frame(annotated_image, potential_frame_path)
                if save_success:
                    frame_path = str(potential_frame_path)
                    logging.info(f"检测到异常，已保存YOLO标注图片: {frame_path}")
                else:
                    frame_path = ''
                    logging.error(f"异常情况下保存YOLO标注图片失败")
            else:
                # 如果没有标注图像，保存原始图像
                save_success = self._save_frame(resized_frame, potential_frame_path)
                if save_success:
                    frame_path = str(potential_frame_path)
                    logging.info(f"检测到异常，已保存原始图片: {frame_path}")
                else:
                    frame_path = ''
                    logging.error(f"异常情况下保存原始图片失败")
        else:
            # 正常情况，不保存图片
            coverage_float = 1
            frame_path = ''  # 正常情况不保存图片，路径为空
            logging.info(f"滤池检测结果正常，不需要保存图片")
            
        all_situation_analysis = response_dict.get('你的思考', '')

        # 处理重复报警抑制逻辑
        alarm_status_flag, is_abnormal = self._handle_filter_alarm_suppression(
            camera_id, value, coverage_float, threshold, current_time, failure_reasons_type
        )
        
        logging.info(f"滤池处理器处理完成 - 摄像头ID: {camera_id}, 覆盖率: {coverage_float}, 警报状态: {alarm_status_flag}, 图片路径: {frame_path}")
        if failure_reasons_type:
            logging.warning(f"检测到故障类型: {', '.join(failure_reasons_type)}")
        
        return (coverage_float, all_situation_analysis, alarm_status_flag, is_abnormal,
                frame_path, analysis_result, failure_reasons_type)

    def _process_image_comparison_with_annotation(self, frame, frame_path, standard_image_path=None, system_type=None):
        """处理图像比较并返回YOLO标注图像

        Args:
            frame: 当前帧图像
            frame_path: 帧图像保存路径
            standard_image_path: 标准图像路径
            system_type: 系统类型

        Returns:
            tuple: (图像分析结果, YOLO标注图像)
        """
        # 调用原有的图像比较方法
        response_dict = self._process_image_comparison(frame, frame_path, standard_image_path, system_type)

        # 获取YOLO标注图像
        annotated_image = None
        if hasattr(self.processor, '_get_last_annotated_image'):
            annotated_image = self.processor._get_last_annotated_image()

        return response_dict, annotated_image

    def _save_annotated_frame(self, annotated_image, frame_path):
        """保存YOLO标注后的图像

        Args:
            annotated_image: YOLO标注后的图像数组
            frame_path: 保存路径

        Returns:
            bool: 保存是否成功
        """
        try:
            import cv2
            cv2.imwrite(str(frame_path), annotated_image)
            return True
        except Exception as e:
            logging.error(f"保存YOLO标注图片失败: {str(e)}")
            return False

    def _handle_filter_alarm_suppression(self, camera_id, detection_result, coverage_float, threshold, current_time, failure_reasons_type):
        """处理滤池重复报警抑制逻辑

        Args:
            camera_id: 摄像头ID
            detection_result: 检测结果（'是' 或 '否'）
            coverage_float: 覆盖率
            threshold: 阈值
            current_time: 当前时间
            failure_reasons_type: 故障类型列表

        Returns:
            tuple: (警报状态, 是否异常)
        """
        # 检查是否启用重复报警抑制
        if not self.processor.filter_alarm_suppression_enabled:
            # 未启用抑制功能，使用原有逻辑
            return self._determine_alarm_status(coverage_float, threshold)

        # 确保当前时间不为None
        if current_time is None:
            current_time = datetime.now()

        # 初始化摄像头状态（如果不存在）
        if camera_id not in self.processor.filter_fault_state:
            self.processor.filter_fault_state[camera_id] = {
                "fault_count": 0,
                "first_detection_time": None,
                "alarm_triggered": False,
                "last_detection_time": None
            }

        camera_state = self.processor.filter_fault_state[camera_id]

        if detection_result == '是':
            # 检测到故障
            current_fault_count = len([reason for reason in failure_reasons_type if '曝气头' in reason])

            # 如果是首次检测到故障
            if not camera_state["alarm_triggered"]:
                camera_state["fault_count"] = current_fault_count
                camera_state["first_detection_time"] = current_time
                camera_state["alarm_triggered"] = True
                camera_state["last_detection_time"] = current_time

                logging.info(f"摄像头 {camera_id} 首次检测到滤池故障，故障数量: {current_fault_count}，触发报警")
                return self._determine_alarm_status(coverage_float, threshold)

            # 已经触发过报警，检查是否在抑制窗口内
            time_since_first = (current_time - camera_state["first_detection_time"]).total_seconds()

            if time_since_first <= self.processor.filter_alarm_suppression_window:
                # 在抑制窗口内，检查故障数量是否有变化
                if current_fault_count == camera_state["fault_count"]:
                    # 故障数量未变化，抑制报警
                    camera_state["last_detection_time"] = current_time
                    logging.info(f"摄像头 {camera_id} 在抑制窗口内({time_since_first:.1f}秒)，故障数量未变化({current_fault_count})，抑制报警")
                    return 'NO_ALARM', False
                else:
                    # 故障数量有变化，重新报警
                    old_fault_count = camera_state["fault_count"]
                    camera_state["fault_count"] = current_fault_count
                    camera_state["first_detection_time"] = current_time  # 重置时间窗口
                    camera_state["last_detection_time"] = current_time
                    logging.info(f"摄像头 {camera_id} 故障数量发生变化(从 {old_fault_count} 到 {current_fault_count})，重新触发报警")
                    return self._determine_alarm_status(coverage_float, threshold)
            else:
                # 超出抑制窗口，重新开始计时
                camera_state["fault_count"] = current_fault_count
                camera_state["first_detection_time"] = current_time
                camera_state["last_detection_time"] = current_time
                logging.info(f"摄像头 {camera_id} 超出抑制窗口({time_since_first:.1f}秒 > {self.processor.filter_alarm_suppression_window}秒)，重新触发报警")
                return self._determine_alarm_status(coverage_float, threshold)

        else:
            # 检测结果正常，重置状态
            if camera_state["alarm_triggered"]:
                logging.info(f"摄像头 {camera_id} 滤池故障已恢复正常，重置报警状态")
                camera_state["fault_count"] = 0
                camera_state["first_detection_time"] = None
                camera_state["alarm_triggered"] = False
                camera_state["last_detection_time"] = None

            return self._determine_alarm_status(coverage_float, threshold)