# -------------------------------------------------------- #
#                这个代码用于根据点的坐标切分图片,然后对图片进行分割,不涉及其他变换                #
# -------------------------------------------------------- #
"""
主要功能：
- 从单张图片中提取指定坐标区域

"""
import cv2
import numpy as np
import os
from typing import List, Tuple, Union, Optional
import glob

def load_coordinates(coord_file: str) -> List[Tuple[float, float]]:
    """
    从坐标文件中加载坐标点
    
    Args:
        coord_file: 坐标文件路径
        
    Returns:
        坐标点列表
    """
    coordinates = []
    with open(coord_file, 'r') as f:
        lines = f.readlines()
        for line in lines:
            if line.startswith("Point "):
                # 解析格式为 "Point N: X, Y" 的行
                parts = line.split(": ")[1].strip().split(", ")
                if len(parts) == 2:
                    x, y = float(parts[0]), float(parts[1])
                    coordinates.append((x, y))
    
    return coordinates

def extract_region(image, coordinates: List[Tuple[float, float]], trim_border: bool = False) -> np.ndarray:
    """
    从图像中提取指定坐标区域
    
    Args:
        image: OpenCV格式的图像
        coordinates: 坐标点列表，每个点为(x, y)元组
        trim_border: 是否裁剪掉周围的黑色边框，只保留坐标框选的区域
        
    Returns:
        带透明通道的提取区域图像
    """
    if len(coordinates) < 3:
        raise ValueError("坐标点数量不足，至少需要3个点形成闭合区域")
    
    # 转换坐标为整数类型的numpy数组
    pts_array = np.array(coordinates, dtype=np.int32)
    
    # 计算包含多边形的最小矩形区域
    x, y, w, h = cv2.boundingRect(pts_array)
    
    # 从原图中裁剪出这个矩形区域
    cropped_image = image[y:y+h, x:x+w]
    
    # 将原始坐标点平移到裁剪后图像的坐标系中
    pts_array_cropped = pts_array - np.array([x, y])
    
    # 在裁剪后的尺寸上创建蒙版
    cropped_mask = np.zeros(cropped_image.shape[:2], dtype=np.uint8)
    cv2.fillPoly(cropped_mask, [pts_array_cropped], (255,))
    
    # 将蒙版应用于裁剪后的图像，并添加Alpha通道
    result_bgr = cv2.bitwise_and(cropped_image, cropped_image, mask=cropped_mask)
    b, g, r = cv2.split(result_bgr)
    final_result = cv2.merge([b, g, r, cropped_mask])  # cropped_mask作为alpha通道
    
    # 如果需要裁剪掉黑色边框，只保留实际的多边形区域
    if trim_border:
        # 找到蒙版中非零区域的边界
        contours, _ = cv2.findContours(cropped_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if contours:
            # 获取最大轮廓的边界矩形
            x_min, y_min, w_min, h_min = cv2.boundingRect(contours[0])
            # 裁剪到只包含非零区域
            final_result = final_result[y_min:y_min+h_min, x_min:x_min+w_min]
    
    return final_result
def extract_from_image(image_path: str, coordinates: Union[List[Tuple[float, float]], str], 
                      save: bool = True, output_path: Optional[str] = None,
                      trim_border: bool = False) -> np.ndarray:
    """
    从单张图片中提取指定坐标区域
    
    Args:
        image_path: 输入图片路径
        coordinates: 坐标点列表或坐标文件路径
        save: 是否保存提取结果
        output_path: 输出文件路径，如果为None则基于输入文件名自动生成
        trim_border: 是否裁剪掉周围的黑色边框，只保留坐标框选的区域
        
    Returns:
        提取的图像区域
    """
    # 检查图片是否存在
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"找不到图片文件: {image_path}")
    
    # 加载图片
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"无法加载图片: {image_path}")
    
    # 处理坐标输入
    if isinstance(coordinates, str):
        # 如果是字符串，则视为坐标文件路径
        if not os.path.exists(coordinates):
            raise FileNotFoundError(f"找不到坐标文件: {coordinates}")
        coordinates = load_coordinates(coordinates)
    
    # 提取区域
    extracted_image = extract_region(image, coordinates, trim_border=trim_border)
    
    # 保存结果
    if save:
        if output_path is None:
            # 自动生成输出路径
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            output_dir = os.path.dirname(image_path)
            output_path = os.path.join(output_dir, f"{base_name}_extracted.png")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
        
        # 保存图片
        cv2.imwrite(output_path, extracted_image)
        print(f"已保存提取图像到: {output_path}")
    
    return extracted_image

if __name__ == "__main__":
    image_path = "assets/robot_pool_4050_20250608_064449.jpg"
    coordinates = "llms/utils/split_coords/slag_outletv2_coords.txt"
    output_path = "tests/slag_outletv2_extracted.jpg"
    extract_from_image(image_path, coordinates, save=True, output_path=output_path, trim_border=True)