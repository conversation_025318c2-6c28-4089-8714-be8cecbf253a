import logging
import sys
import os
# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from server.remote.device import get_device_list,process_device_data,process_robot_and_cameras_status
from server.utils.logger import setup_logging
from server.remote.device import get_robot_status
# 配置日志
setup_logging()
logger = logging.getLogger(__name__)


def get_cameras_config() :
    """获取摄像头配置列表

    Returns:
        List[Dict]: 摄像头配置列表
    """
    try:
        # 使用api1_test.py中的方式获取设备列表
        result = get_device_list(main_type="cam")
        if not result:
            logging.error("获取设备列表失败")
            return []

        # 处理设备数据并构建配置
        camera_configs = process_device_data(result)
        logging.info(f"成功获取 {len(camera_configs)} 个摄像头配置")
        return camera_configs

    except Exception as e:
        logging.error(f"获取摄像头配置时发生错误: {str(e)}")
        return []
# 用于获取机器人和其绑定的摄像头状态
def get_robot_and_cameras_status():
    """获取机器人和其绑定的摄像头状态
    
    Returns:
        Dict: 包含机器人和其绑定的摄像头状态信息的字典，格式为：
        {
            "robot_id": {
                "camera_configs": [摄像头配置列表],
                "other_device_ids": [其他设备ID列表，如浓度计等]
            }
        }
    """
    try:
        # 使用api1_test.py中的方式获取设备列表
        result = get_device_list(main_type="cam")
        if not result:
            logging.error("获取设备列表失败")
            return []

        # 处理设备数据并构建配置
        camera_configs = process_robot_and_cameras_status(result) # 这里获取到绑定的机器人和对应的摄像头
        logging.info(f"成功获取 {len(camera_configs)} 个机器人")
        return camera_configs

    except Exception as e:
        logging.error(f"获取摄像头配置时发生错误: {str(e)}")
        return []
# 获取机器人状态
def get_robot_status_info(robot_id):
    """获取机器人状态,字段如下:
        # 机器人状态
        running_status: 1 # 1表示机器人正在运行,0表示机器人正在停止
        
        # 撇渣管状态
        slag_pipe: {
            "ai_control_status": "-",
            "remote_control_status": "-",
            "on_site_control_status": "-",
            "open_valve_status": "-",
            "close_valve_status": "-"
        }
        
        # 排渣堰门状态
        slag_weir_gate: {
            "ai_control_status": "-",
            "remote_control_status": "-",
            "on_site_control_status": "-",
            "open_gate_status": "-",
            "close_gate_status": "-"
        }
        
        # 套筒阀状态
        sleeve_valve: {
            "ai_control_status": "-",
            "remote_control_status": "-",
            "on_site_control_status": "-",
            "open_valve_status": "-",
            "close_valve_status": "-"
        }
    """
    return get_robot_status(robot_id)
    
if __name__ == "__main__":
    cameras = get_cameras_config()
    print(cameras)
    # ======================== 获取机器人状态 ======================= #
    robot_status = get_robot_status_info()
    print(robot_status)
    
    # ======================== 获取机器人和摄像头映射 ======================= #
    robot_cameras = get_robot_and_cameras_status()
    print(f"获取到 {len(robot_cameras)} 个机器人")
    
    # 打印每个机器人的摄像头和其他设备信息
    for robot_id, device_data in robot_cameras.items():
        camera_configs = device_data.get('camera_configs', [])
        other_device_ids = device_data.get('other_device_ids', [])
        print(f"机器人 {robot_id}:")
        print(f"  - 摄像头数量: {len(camera_configs)}")
        print(f"  - 其他设备ID: {other_device_ids}")
