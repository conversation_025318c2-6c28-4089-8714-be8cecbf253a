import os
from pathlib import Path
from config_file import config
import yaml

"""
路径管理模块

主要功能：
1. 管理项目中的各种路径配置
2. 处理环境相关的路径变量
3. 提供API URL的获取接口
"""


class PathManager:
    """路径管理器类
    
    负责管理项目中的所有路径配置，支持不同环境的路径解析
    
    Attributes:
        env_config (dict): 环境配置信息
        env_type (str): 当前环境类型（development/production）
        base_paths (dict): 基础路径配置字典
    """

    def __init__(self):
        """初始化路径管理器
        
        初始化流程：
        1. 获取项目根目录
        2. 加载环境配置文件
        3. 确定当前环境类型
        4. 初始化基础路径
        """
        # 获取当前文件所在目录
        # current_dir = Path(__file__).parent.parent.parent

        # 加载环境配置
        # env_config_path = current_dir / 'configs' / 'env.yaml'
        # with open(env_config_path, 'r', encoding='utf-8') as f:
        #     self.env_config = yaml.safe_load(f)
        self.env_config = config.env

        # 获取环境类型
        self.env_type = os.getenv('APP_ENV', self.env_config['environment']['type'])

        # 初始化基础路径
        self.base_paths = self._init_base_paths()

    def _init_base_paths(self) -> dict:
        """初始化所有基础路径
        
        处理配置文件中的路径，解析其中的变量
        
        Returns:
            dict: 解析后的路径字典，包含：
                - project_root: 项目根目录
                - 其他在配置文件中定义的路径
                
        特点：
        1. 支持路径中的变量替换
        2. 使用Path对象确保跨平台兼容
        """
        paths = self.env_config['environment']['base_paths']

        # 替换路径中的变量
        resolved_paths = {}
        for key, path in paths.items():
            if key == 'project_root':
                resolved_paths[key] = Path(path)
            else:
                # 替换路径中的项目根目录变量
                resolved_path = path.format(
                    project_root=str(resolved_paths['project_root'])
                )
                resolved_paths[key] = Path(resolved_path)

        return resolved_paths

    def get_path(self, path_key: str) -> Path:
        """获取指定的路径
        
        Args:
            path_key (str): 路径键名，必须在配置文件中定义
            
        Returns:
            Path: 解析后的路径对象
            
        Raises:
            KeyError: 当请求的路径键不存在时
        """
        if path_key not in self.base_paths:
            raise KeyError(f"未找到路径配置: {path_key}")
        return self.base_paths[path_key]

    def get_api_url(self, url_key: str) -> str:
        """获取指定环境的API URL
        
        根据当前环境类型返回对应的API URL
        
        Args:
            url_key (str): URL配置键名
            
        Returns:
            str: 完整的API URL
            
        配置示例：
            api:
              development:
                base_url: http://dev-api.example.com
              production:
                base_url: http://api.example.com
        """
        return self.env_config['api'][self.env_type][url_key]
