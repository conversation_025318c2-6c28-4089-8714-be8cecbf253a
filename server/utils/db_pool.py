"""
数据库连接池优化模块
用于减少数据库连接开销，提升查询性能
"""
import psycopg2
from psycopg2 import pool
import threading
import logging
from contextlib import contextmanager
from config_file import config

class DatabasePool:
    """数据库连接池单例类"""
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(DatabasePool, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
        
        try:
            # 从配置文件获取数据库连接信息
            db_config = config.env['database']['postgres']
            
            # 创建连接池
            self._pool = psycopg2.pool.ThreadedConnectionPool(
                minconn=5,      # 最小连接数
                maxconn=20,     # 最大连接数
                host=db_config['host'],
                port=db_config['port'],
                database=db_config['dbname'],
                user=db_config['user'],
                password=db_config['password'],
                # 连接超时设置
                connect_timeout=10,
                # 其他优化参数
                options='-c statement_timeout=30000'  # 30秒查询超时
            )
            self._initialized = True
            logging.info("数据库连接池初始化成功")
            
        except Exception as e:
            logging.error(f"数据库连接池初始化失败: {e}")
            self._pool = None
            self._initialized = False
            raise
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        if not self._pool:
            raise Exception("数据库连接池未初始化")
        
        conn = None
        try:
            # 从连接池获取连接
            conn = self._pool.getconn()
            if conn:
                yield conn
            else:
                raise Exception("无法从连接池获取连接")
        except Exception as e:
            if conn:
                # 如果连接有问题，回滚事务
                try:
                    conn.rollback()
                except:
                    pass
            logging.error(f"数据库连接错误: {e}")
            raise
        finally:
            if conn:
                # 将连接归还给连接池
                self._pool.putconn(conn)
    
    def close_all_connections(self):
        """关闭所有连接"""
        if self._pool:
            self._pool.closeall()
            logging.info("所有数据库连接已关闭")

# 创建全局连接池实例
db_pool = DatabasePool()

def get_db_connection():
    """获取数据库连接（兼容原有代码的函数）"""
    return db_pool.get_connection()

# 优化后的查询函数
def query_data_optimized(table, conditions=None, fields=None, order_by=None, limit=None):
    """
    使用连接池的优化查询函数
    
    Args:
        table (str): 表名
        conditions (dict): 查询条件
        fields (list): 查询字段
        order_by (str): 排序条件
        limit (int): 限制返回记录数

    Returns:
        list: 查询结果列表
    """
    try:
        with db_pool.get_connection() as conn:
            cur = conn.cursor()
            
            # 构建查询字段
            if fields:
                fields_str = ', '.join(fields)
            else:
                fields_str = '*'
                
            # 构建查询条件
            query_sql = f"SELECT {fields_str} FROM {table}"
            condition_values = []
            
            if conditions:
                condition_str = ' AND '.join([f"{key} = %s" for key in conditions.keys()])
                condition_values = list(conditions.values())
                query_sql += f" WHERE {condition_str}"
                
            # 添加排序
            if order_by:
                query_sql += f" ORDER BY {order_by}"
                
            # 添加结果数量限制
            if limit:
                query_sql += f" LIMIT {limit}"
                
            cur.execute(query_sql, condition_values)
            result = cur.fetchall()
            
            return result
            
    except Exception as e:
        logging.error(f"查询数据错误: {e}")
        return []

def batch_query_by_camera_ids(camera_ids, fields=None):
    """
    批量查询多个camera_id的数据（同步函数，供run_in_threadpool使用）
    专门用于优化 query_by_cameras 接口
    
    Args:
        camera_ids (list): 摄像头ID列表
        fields (list): 查询字段列表
        
    Returns:
        list: 查询结果列表
        
    Raises:
        Exception: 数据库查询错误
    """
    if not camera_ids:
        return []
        
    with db_pool.get_connection() as conn:
        cur = conn.cursor()
        
        # 默认查询字段
        if not fields:
            fields = [
                "camera_id", "video_id", "frame_number", "timestamp", "frame_path",
                "coverage_rate", "coverage_level", "alarm_status", "analysis_detail",
                "is_abnormal", "do_value", "mlss_value", "adjustment_suggestion",
                "is_read", "failure_reasons_type", "failure_reasons_number",
                "alarmtype"
            ]
        
        fields_str = ', '.join(fields)
        placeholders = ', '.join(['%s'] * len(camera_ids))
        
        query_sql = f"""
            SELECT {fields_str}
            FROM frame_analysis_latest 
            WHERE camera_id IN ({placeholders})
            ORDER BY camera_id, timestamp DESC
        """
        
        cur.execute(query_sql, camera_ids)
        results = cur.fetchall()
        
        return results
